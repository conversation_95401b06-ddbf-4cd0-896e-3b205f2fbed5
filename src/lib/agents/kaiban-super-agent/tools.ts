/**
 * KaibanJS Tools Configuration
 * 
 * This file configures the Tavily search tool for KaibanJS agents
 * following the pattern from the user's memories and KaibanJS documentation.
 */

import { TavilySearchResults } from '@kaibanjs/tools';

// Console logging utility for Kaiban Tools
const logKaibanTool = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`🔧 [KAIBAN-TOOL] ${timestamp}: ${message}`);
  if (data) {
    console.log(`📝 [KAIBAN-TOOL-DATA]:`, data);
  }
};

/**
 * Tavily Search Tool Configuration
 * 
 * Following the KaibanJS Tavily search tool implementation pattern:
 * - Import TavilySearchResults from @kaibanjs/tools
 * - Instantiate with apiKey and maxResults parameters
 * - Add to agent's tools array
 */
logKaibanTool('Initializing Tavily Search Tool');

export const tavilySearchTool = new TavilySearchResults({
  apiKey: process.env.TAVILY_API_KEY || '',
  maxResults: 10,
  // Additional configuration options
  includeAnswer: true,
  includeRawContent: true,
  searchDepth: 'deep',
  includeImages: false,
  includeDomains: [],
  excludeDomains: ['reddit.com', 'quora.com'], // Exclude low-quality sources
});

logKaibanTool('Tavily Search Tool configured', {
  maxResults: 10,
  searchDepth: 'deep',
  includeAnswer: true,
  includeRawContent: true,
  apiKeyConfigured: !!process.env.TAVILY_API_KEY
});

/**
 * Tool validation function
 */
export const validateTavilyTool = (): boolean => {
  const isValid = !!process.env.TAVILY_API_KEY;
  
  if (!isValid) {
    logKaibanTool('❌ Tavily API key not configured', {
      error: 'TAVILY_API_KEY environment variable is missing',
      solution: 'Add your Tavily API key to the .env.local file'
    });
  } else {
    logKaibanTool('✅ Tavily tool validation passed');
  }
  
  return isValid;
};

/**
 * Export tools array for easy import in agents
 */
export const kaibanTools = [tavilySearchTool];

logKaibanTool('KaibanJS Tools initialization complete', {
  totalTools: kaibanTools.length,
  toolNames: ['TavilySearchResults'],
  status: 'ready'
});
