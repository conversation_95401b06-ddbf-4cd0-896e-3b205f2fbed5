(()=>{var e={};e.id=22,e.ids=[22],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13767:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{POST:()=>d,PUT:()=>h});var n=r(96559),i=r(48088),s=r(37719),o=r(32190),c=r(93356),l=r(99475);class u{constructor(e){this.phaseTimings={},this.errorCount=0,this.retryCount=0,this.gemini=new c.p,this.searchService=new l.J,this.onProgress=e}updateProgress(e,t,r,a){this.onProgress&&this.onProgress({phase:e,step:t,progress:r,message:a})}async withRetry(e,t=3,r=1e3){let a;for(let n=0;n<=t;n++)try{return await e()}catch(e){a=e,this.errorCount++,n<t&&(this.retryCount++,console.warn(`Attempt ${n+1} failed, retrying in ${r}ms:`,e),await new Promise(e=>setTimeout(e,r*Math.pow(2,n))))}throw a}trackPhaseTime(e,t){let r=Date.now();return t().finally(()=>{this.phaseTimings[e]=Date.now()-r})}calculateSourceAuthority(e,t){let r=50;return["edu","gov","org","nature.com","science.org","ieee.org","pubmed.ncbi.nlm.nih.gov","scholar.google.com","arxiv.org"].some(e=>t.includes(e))&&(r+=30),e.startsWith("https://")&&(r+=10),["blog","forum","wiki","reddit"].some(e=>t.includes(e))&&(r-=20),Math.max(0,Math.min(100,r))}determineSourceType(e){return e.includes(".edu")||e.includes(".gov")?"official":e.includes("news")||e.includes("times")||e.includes("post")?"news":e.includes("blog")||e.includes("medium")?"blog":e.includes("reddit")||e.includes("forum")?"forum":e.includes("scholar")||e.includes("pubmed")||e.includes("arxiv")?"academic":"unknown"}async analyzeTopicWithGemini(e,t={}){return this.trackPhaseTime("analysis",async()=>{this.updateProgress("analysis","topic-analysis",10,"Analyzing topic with Gemini AI...");let r=`
Analyze this topic for comprehensive research and professional content creation: "${e}"

You are an expert content strategist and research analyst with deep knowledge across multiple domains. Provide a detailed analysis for a comprehensive research workflow that will produce authoritative, engaging, well-researched content.

Return your analysis in the following structured text format using clear delimiters:

===MAIN_TOPIC===
[primary topic focus]

===SUBTOPICS===
- [subtopic1]
- [subtopic2]
- [subtopic3]
- [subtopic4]
- [subtopic5]

===KEY_TERMS===
- [term1]
- [term2]
- [term3]
- [term4]
- [term5]
- [term6]
- [term7]

===SEARCH_QUERIES===
- [query1]
- [query2]
- [query3]
- [query4]

===COMPLEXITY===
[simple|moderate|complex]

===ESTIMATED_SOURCES===
[number]

===AUDIENCE_INTENT===
[what the target audience wants to achieve]

===CONTENT_ANGLE===
[unique perspective or approach for this topic]

===COMPETITIVE_KEYWORDS===
- [keyword1]
- [keyword2]
- [keyword3]

===TRENDING_TOPICS===
- [trend1]
- [trend2]
- [trend3]

===CONTENT_GAPS===
- [gap1]
- [gap2]
- [gap3]

===USER_QUESTIONS===
- [question1]
- [question2]
- [question3]

===SEMANTIC_KEYWORDS===
- [semantic1]
- [semantic2]
- [semantic3]

Enhanced Guidelines:
- Identify 4-6 key subtopics that provide comprehensive coverage
- Extract 6-8 important key terms including technical terminology and related concepts
- Create 3-4 strategic search queries that will capture diverse, authoritative sources
- Include both broad and specific search terms
- Assess complexity based on topic breadth, technical depth, and research requirements
- Estimate realistic source count for thorough coverage (typically 8-15 sources)
- Consider current trends and recent developments in the field
- Focus on queries that will find academic, official, and high-authority sources

Professional Writing Analysis:
- Analyze audience intent: What does the target audience want to achieve or learn?
- Identify unique content angle: What fresh perspective can we bring to this topic?
- Find competitive keywords: What terms are competitors ranking for?
- Spot trending topics: What related topics are currently popular?
- Identify content gaps: What information is missing from existing content?
- Generate user questions: What questions would the audience ask about this topic?
- Extract semantic keywords: What related terms and concepts should be included?

Target content type: ${t.contentType||"article"}
Target audience: ${t.targetAudience||"general"}
Content purpose: ${t.contentPurpose||"inform"}
Target audience expertise: ${"academic"===t.tone?"expert":"professional"===t.tone?"intermediate":"general"}
`;return this.withRetry(async()=>{let e=await this.gemini.generateContent(r,{temperature:.2,maxOutputTokens:1200});try{let t=this.parseTopicAnalysis(e);if(!t.mainTopic||!t.subtopics||!t.keyTerms||!t.searchQueries)throw Error("Missing required fields in topic analysis");return this.updateProgress("analysis","topic-analysis",20,"Topic analysis completed"),t}catch(t){throw console.error("Structured text parsing failed, raw response:",e),Error(`Failed to parse topic analysis: ${t}`)}},t.retryAttempts||3)})}async generateContentStrategy(e,t,r={}){this.updateProgress("strategy","strategy-development",25,"Developing content strategy...");let a=`
Create a comprehensive content strategy for: "${e}"

Based on the topic analysis:
- Main topic: ${t.mainTopic}
- Audience intent: ${t.audienceIntent}
- Content angle: ${t.contentAngle}
- User questions: ${t.userQuestions.join(", ")}
- Content gaps: ${t.contentGaps.join(", ")}

Content Configuration:
- Content type: ${r.contentType||"article"}
- Target audience: ${r.targetAudience||"general"}
- Content purpose: ${r.contentPurpose||"inform"}
- Tone: ${r.tone||"professional"}
- Word count: ${r.targetWordCount||2e3}
- Content structure: ${r.contentStructure||"analytical"}

Return a detailed content strategy using structured text format with clear delimiters:

===CONTENT_GOAL===
[primary objective of the content]

===TARGET_AUDIENCE===
[detailed audience description]

===CONTENT_STRUCTURE===
[recommended structure approach]

===KEY_MESSAGES===
- [message1]
- [message2]
- [message3]

===HOOKS===
- [hook1]
- [hook2]
- [hook3]

===CALL_TO_ACTIONS===
- [cta1]
- [cta2]
- [cta3]

===SEO_STRATEGY===
primaryKeywords: [keyword1, keyword2]
secondaryKeywords: [keyword3, keyword4, keyword5]
searchIntent: [informational|navigational|transactional|commercial]
competitorGaps: [gap1, gap2]

===NARRATIVE_STRUCTURE===
opening: [how to start the content]
development: [section1, section2, section3]
climax: [key insight or turning point]
resolution: [conclusion and next steps]

Focus on creating engaging, actionable content that serves the audience's needs while achieving business objectives.
`;try{let e=await this.gemini.generateContent(a,{temperature:.3,maxOutputTokens:2e3}),t=this.parseContentStrategy(e);return this.updateProgress("strategy","strategy-completed",30,"Content strategy developed"),t}catch(e){throw console.error("Content strategy generation error:",e),Error("Failed to generate content strategy")}}async generateContentOutline(e,t,r,a={}){this.updateProgress("outline","outline-generation",32,"Creating content outline...");let n=`
Create a detailed content outline for: "${e}"

Based on:
- Content strategy goal: ${r.contentGoal}
- Target audience: ${r.targetAudience}
- Key messages: ${r.keyMessages.join(", ")}
- Topic analysis: ${t.mainTopic} with subtopics: ${t.subtopics.join(", ")}
- Target word count: ${a.targetWordCount||2e3}
- Content structure: ${a.contentStructure||"analytical"}

Return a comprehensive outline using structured text format with clear delimiters:

===TITLE===
[compelling title for the content]

===INTRODUCTION===
hook: [attention-grabbing opening]
context: [background information]
thesis: [main argument or purpose]

===SECTIONS===
SECTION 1
Heading: [section title]
Subheadings:
- [subheading1]
- [subheading2]
Key Points:
- [point1]
- [point2]
- [point3]
Supporting Data:
- [data1]
- [data2]
Sources:
- [source requirement 1]
- [source requirement 2]

SECTION 2
Heading: [section title]
Subheadings:
- [subheading1]
- [subheading2]
Key Points:
- [point1]
- [point2]
- [point3]
Supporting Data:
- [data1]
- [data2]
Sources:
- [source requirement 1]
- [source requirement 2]

===CONCLUSION===
summary: [key takeaways]
callToAction: [specific action for readers]
nextSteps: [step1, step2, step3]

===ESTIMATED_WORD_COUNT===
[number]

===READING_TIME===
[number in minutes]

===DIFFICULTY_LEVEL===
[beginner|intermediate|advanced]

Create an outline that ensures comprehensive coverage while maintaining reader engagement throughout.
`;try{let e=await this.gemini.generateContent(n,{temperature:.3,maxOutputTokens:2500}),t=this.parseContentOutline(e);return this.updateProgress("outline","outline-completed",35,"Content outline created"),t}catch(e){throw console.error("Content outline generation error:",e),Error("Failed to generate content outline")}}async conductPrimaryResearch(e,t=5,r={}){return this.trackPhaseTime("primary-research",async()=>{this.updateProgress("primary-research","rapid-search",30,"Conducting rapid exact search of topic...");let a=e.mainTopic;console.log(`🎯 Rapid exact search for: "${a}"`);let n=await this.searchService.searchAndExtract(a,10);if(!n?.extractedContent?.length){for(let t of(console.log(`⚠️ No results for exact search, trying broader terms...`),e.keyTerms.slice(0,3)))if(console.log(`🔍 Fallback search: "${t}"`),n=await this.searchService.searchAndExtract(t,10),n?.extractedContent?.length)break;if(!n?.extractedContent?.length&&e.searchQueries.length>0){for(let t of(console.log(`🔍 Fallback to original queries...`),e.searchQueries.slice(0,2)))if(n=await this.searchService.searchAndExtract(t,10),n?.extractedContent?.length)break}if(!n?.extractedContent?.length){let t=e.mainTopic.split(" ").slice(0,2).join(" ");console.log(`🔍 Final fallback search: "${t}"`),n=await this.searchService.searchAndExtract(t,10)}}if(!n?.extractedContent?.length)throw Error(`No search results found for topic "${e.mainTopic}" or related terms. This topic may not exist or may be too specific. Please try a different topic or check the spelling.`);this.updateProgress("primary-research","content-analysis",40,"Analyzing top search results with Gemini...");let i=n.extractedContent.slice(0,5),s=await this.analyzeTopPagesForQueries(e.mainTopic,i);console.log(`🧠 Generated ${s.targetedQueries.length} targeted queries based on real content analysis`),this.updateProgress("primary-research","targeted-search",50,"Executing targeted queries based on content analysis...");let o=[],c=this.processSearchResults(n,e,"rapid-initial");for(let r of(o.push(...c),s.targetedQueries))try{console.log(`� Targeted search: ${r.query}`);let a=await this.searchService.searchAndExtract(r.query,t);if(a?.extractedContent?.length){let t=this.processSearchResults(a,e,`targeted-${Date.now()}`);t.forEach(e=>{e.metadata.queryType=r.type,e.metadata.sourceQuery=r.query,e.metadata.queryStrategy=r.strategy}),o.push(...t)}await new Promise(e=>setTimeout(e,800))}catch(e){console.error(`Targeted query error for "${r.query}":`,e);continue}let l=this.removeDuplicateResearch(o),u=this.filterByQuality(l,r.qualityThreshold||30).sort((e,t)=>{let r=t.relevanceScore+.3*(t.metadata.authorityScore||0);return e.relevanceScore+.3*(e.metadata.authorityScore||0)-r});return console.log(`📄 Collected ${u.length} quality sources from ${o.length} total results`),this.updateProgress("primary-research","content-extraction",60,`Extracted content from ${u.length} high-quality sources`),u})}async analyzeTopPagesForQueries(e,t){let r=t.map((e,t)=>`Page ${t+1} (${e.url}):
${e.content.substring(0,800)}...
`).join("\n---\n"),a=`
Analyze these top search results for "${e}" and generate targeted search queries based on the actual content patterns you observe.

TOP SEARCH RESULTS CONTENT:
${r}

Based on your analysis of these real search results, generate targeted queries that will find specific information gaps, expert insights, and detailed coverage that's missing or could be expanded upon.

Return your analysis using structured text format with clear delimiters:

===TARGETED_QUERIES===
QUERY 1
Query: [specific search query based on content analysis]
Type: [content-gap|specific-detail|expert-insight|recent-update|comparison|case-study]
Strategy: [search strategy description]
Priority: [high|medium|low]
Reasoning: [why this query is needed based on content analysis]

QUERY 2
Query: [specific search query based on content analysis]
Type: [content-gap|specific-detail|expert-insight|recent-update|comparison|case-study]
Strategy: [search strategy description]
Priority: [high|medium|low]
Reasoning: [why this query is needed based on content analysis]

QUERY 3
Query: [specific search query based on content analysis]
Type: [content-gap|specific-detail|expert-insight|recent-update|comparison|case-study]
Strategy: [search strategy description]
Priority: [high|medium|low]
Reasoning: [why this query is needed based on content analysis]

QUERY 4
Query: [specific search query based on content analysis]
Type: [content-gap|specific-detail|expert-insight|recent-update|comparison|case-study]
Strategy: [search strategy description]
Priority: [high|medium|low]
Reasoning: [why this query is needed based on content analysis]

QUERY 5
Query: [specific search query based on content analysis]
Type: [content-gap|specific-detail|expert-insight|recent-update|comparison|case-study]
Strategy: [search strategy description]
Priority: [high|medium|low]
Reasoning: [why this query is needed based on content analysis]

===CONTENT_PATTERNS===
- [pattern1 observed in the content]
- [pattern2 observed in the content]
- [pattern3 observed in the content]

===KEY_INSIGHTS===
- [insight1 from content analysis]
- [insight2 from content analysis]
- [insight3 from content analysis]

===MISSING_ASPECTS===
- [aspect1 that needs more coverage]
- [aspect2 that needs more coverage]
- [aspect3 that needs more coverage]

Guidelines:
- Generate queries that target specific information gaps you identify in the content
- Focus on finding authoritative sources, expert opinions, and detailed technical information
- Look for opportunities to find recent developments, case studies, and comparative analysis
- Prioritize queries that will significantly enhance the final content quality
- Base all queries on actual patterns and gaps you observe in the provided content
`;try{let e=await this.gemini.generateContent(a,{temperature:.3,maxOutputTokens:2e3});return this.parseRapidAnalysisResult(e)}catch(t){return console.error("Rapid analysis error:",t),{targetedQueries:[{query:`${e} detailed guide`,type:"specific-detail",strategy:"comprehensive-coverage",priority:"high",reasoning:"Fallback query for detailed information"},{query:`${e} expert analysis`,type:"expert-insight",strategy:"authority-focused",priority:"high",reasoning:"Fallback query for expert perspectives"}],contentPatterns:[],keyInsights:[],missingAspects:[]}}}async generateAdvancedQueryStrategy(e){let t=`
Generate a comprehensive multi-layered query strategy for researching: "${e.mainTopic}"

Based on the topic analysis:
- Subtopics: ${e.subtopics.join(", ")}
- Key terms: ${e.keyTerms.join(", ")}
- Complexity: ${e.complexity}
- User questions: ${e.userQuestions.join(", ")}

Create a strategic query decomposition using structured text format:

===BROAD_EXPLORATORY_QUERIES===
- [broad query 1 - for general landscape understanding]
- [broad query 2 - for current trends and developments]
- [broad query 3 - for market/industry overview]

===SPECIFIC_FACTUAL_QUERIES===
- [specific query 1 - for concrete data and statistics]
- [specific query 2 - for technical details and specifications]
- [specific query 3 - for case studies and examples]

===COMPARATIVE_ANALYSIS_QUERIES===
- [comparative query 1 - for alternatives and competitors]
- [comparative query 2 - for pros/cons analysis]
- [comparative query 3 - for benchmarks and comparisons]

===EXPERT_PERSPECTIVE_QUERIES===
- [expert query 1 - for thought leadership content]
- [expert query 2 - for academic research and papers]
- [expert query 3 - for industry expert opinions]

===RECENT_DEVELOPMENTS_QUERIES===
- [recent query 1 - for latest news and updates]
- [recent query 2 - for 2024-2025 developments]
- [recent query 3 - for emerging trends]

Guidelines for query generation:
- Start with broad queries, then get specific (mimicking human research behavior)
- Use different query styles for different information types
- Include temporal modifiers (recent, latest, 2024, 2025) where relevant
- Keep queries concise but descriptive (3-8 words)
- Focus on high-quality, authoritative sources
- Decompose complex topics into atomic sub-questions
`;try{let e=await this.gemini.generateContent(t,{temperature:.3,maxOutputTokens:1500});return this.parseQueryStrategy(e)}catch(t){return console.error("Query strategy generation error:",t),this.createFallbackQueryStrategy(e)}}async executeQueryGroup(e,t,r){let a=[];for(let n of e.queries)try{let i=await this.searchService.searchAndExtract(n,e.resultsPerQuery||r);if(i?.extractedContent?.length){let r=this.processSearchResults(i,t,`${e.type}-${Date.now()}`);r.forEach(t=>{t.metadata.queryType=e.type,t.metadata.sourceQuery=n,t.metadata.queryStrategy=e.strategy||"standard"}),a.push(...r)}}catch(e){console.error(`Query execution error for "${n}":`,e);continue}return a}extractRelevantTopics(e,t){let r=e.toLowerCase(),a=[];return t.subtopics.forEach(e=>{r.includes(e.toLowerCase())&&a.push(e)}),t.keyTerms.forEach(e=>{r.includes(e.toLowerCase())&&a.push(e)}),[...new Set(a)]}processSearchResults(e,t,r){return e.extractedContent.map((a,n)=>{let i=new URL(a.url).hostname,s=this.calculateSourceAuthority(a.url,i),o=this.determineSourceType(i);return{id:`${r}-${Date.now()}-${n}`,url:a.url,title:e.searchResults.find(e=>e.link===a.url)?.title||"Unknown",content:a.content,relevanceScore:this.calculateRelevanceScore(a.content,t.keyTerms),extractedAt:new Date,metadata:{domain:i,wordCount:a.content.split(" ").length,keyTopics:this.extractKeyTopics(a.content,t.keyTerms),authorityScore:s,freshness:this.calculateFreshness(a.url),credibilityScore:this.calculateCredibilityScore(a.content,i),sourceType:o}}})}filterByQuality(e,t){return e.filter(e=>.4*e.relevanceScore+.3*(e.metadata.authorityScore||0)+.2*(e.metadata.credibilityScore||0)+.1*(e.metadata.freshness||0)>=t)}calculateFreshness(e){let t=new Date().getFullYear(),r=e.match(/20\d{2}/);return r?Math.max(0,100-10*(t-parseInt(r[0]))):50}calculateCredibilityScore(e,t){let r=50,a=e.toLowerCase();return["research","study","analysis","data","statistics","peer-reviewed","published","journal","university"].forEach(e=>{a.includes(e)&&(r+=5)}),(t.includes(".edu")||t.includes(".gov"))&&(r+=20),t.includes("wikipedia")&&(r+=10),Math.min(100,r)}async performGapAnalysis(e,t,r){this.updateProgress("gap-analysis","content-analysis",65,"Analyzing content gaps...");let a=r.map(e=>({url:e.url,keyTopics:e.metadata.keyTopics,content:e.content.substring(0,500)})),n=`
Analyze the research data collected for topic: "${e}"

Expected subtopics to cover: ${t.subtopics.join(", ")}
Key terms to address: ${t.keyTerms.join(", ")}

Research data collected:
${a.map(e=>`URL: ${e.url}
Key Topics: ${e.keyTopics.join(", ")}
Content Preview: ${e.content.substring(0,200)}...
`).join("\n---\n")}

Perform a rigorous gap analysis and return results using structured text format with clear delimiters:

===GAPS===
GAP 1
Topic: [topic that needs more coverage]
Importance: [high|medium|low]
Queries: [query1, query2]
Reason: [why this gap exists and why it matters]

GAP 2
Topic: [topic that needs more coverage]
Importance: [high|medium|low]
Queries: [query1, query2]
Reason: [why this gap exists and why it matters]

===COVERAGE_SCORE===
[number from 0-100]

===NEEDS_DEEP_RESEARCH===
[true|false]

===PRIORITY_TOPICS===
- [topic1]
- [topic2]
- [topic3]

Analyze what information is missing, poorly covered, or needs deeper research.
Focus on identifying gaps that would significantly improve the final content quality.
`;try{let e=await this.gemini.generateContent(n,{temperature:.2,maxOutputTokens:1500}),t=this.parseGapAnalysis(e);return this.updateProgress("gap-analysis","priority-scoring",70,"Gap analysis completed"),t}catch(e){throw console.error("Gap analysis error:",e),Error("Failed to perform gap analysis")}}async conductDeepResearch(e,t=3){if(!e.needsDeepResearch)return this.updateProgress("deep-research","skipped",75,"Deep research not needed"),[];this.updateProgress("deep-research","query-generation",75,"Generating deep research queries...");let r=[];for(let a of e.gaps.filter(e=>"high"===e.importance))for(let e of a.suggestedQueries)try{this.updateProgress("deep-research","advanced-extraction",80,`Deep research: ${a.missingTopic}`);let n=await this.searchService.searchAndExtract(e,t),i=n.extractedContent.map((e,t)=>({id:`deep-${Date.now()}-${t}`,url:e.url,title:n.searchResults.find(t=>t.link===e.url)?.title||"Unknown",content:e.content,relevanceScore:this.calculateRelevanceScore(e.content,[a.missingTopic]),extractedAt:new Date,metadata:{domain:new URL(e.url).hostname,wordCount:e.content.split(" ").length,keyTopics:[a.missingTopic]}}));r.push(...i)}catch(t){console.error(`Deep research error for "${e}":`,t)}return this.updateProgress("deep-research","content-structuring",85,`Completed deep research with ${r.length} additional sources`),r}async generateContentWithRAG(e,t,r,a,n={}){this.updateProgress("content-generation","rag-retrieval",90,"Generating professional content with RAG approach...");let i=[...t.primary,...t.deep];if(0===i.length)return console.log("⚠️ No research data available, generating content based on topic knowledge only"),this.generateContentWithoutRAG(e,r,a,n);let s=i.sort((e,t)=>{let r=e.relevanceScore+.3*(e.metadata.authorityScore||0)+.2*(e.metadata.credibilityScore||0);return t.relevanceScore+.3*(t.metadata.authorityScore||0)+.2*(t.metadata.credibilityScore||0)-r}).slice(0,12).map(e=>({url:e.url,title:e.title,content:e.content.substring(0,800),relevance:e.relevanceScore,authority:e.metadata.authorityScore||0,sourceType:e.metadata.sourceType||"unknown"})),o=`
You are a world-class professional content writer and subject matter expert using advanced RAG (Retrieval-Augmented Generation) techniques.

CONTENT BRIEF:
Topic: "${e}"
Content Type: ${n.contentType||"article"}
Target Word Count: ${n.targetWordCount||2e3}
Target Audience: ${n.targetAudience||"general"}
Content Purpose: ${n.contentPurpose||"inform"}
Tone: ${n.tone||"professional"}
Content Structure: ${n.contentStructure||"analytical"}

${r?`CONTENT STRATEGY:
Goal: ${r.contentGoal}
Target Audience: ${r.targetAudience}
Structure: ${r.contentStructure}
Key Messages: ${r.keyMessages.join(", ")}
Hooks: ${r.hooks.join(", ")}
Call to Actions: ${r.callToActions.join(", ")}`:""}

${a?`CONTENT OUTLINE:
Title: ${a.title}
Introduction Hook: ${a.introduction.hook}
Introduction Context: ${a.introduction.context}
Introduction Thesis: ${a.introduction.thesis}
Sections: ${a.sections.map(e=>`${e.heading} (${e.keyPoints.join(", ")})`).join(" | ")}
Conclusion: ${a.conclusion.summary}`:""}

RESEARCH CONTEXT (Retrieved from Knowledge Base):
${s.map(e=>`
Source: ${e.title}
URL: ${e.url}
Relevance: ${e.relevance}/100
Authority: ${e.authority}/100
Type: ${e.sourceType}
Content: ${e.content.substring(0,400)}...
`).join("\n---\n")}

PROFESSIONAL WRITING REQUIREMENTS:

1. STORYTELLING & ENGAGEMENT:
   ${n.enableStorytelling?"- Use narrative techniques and storytelling elements":""}
   ${n.includeHooks?"- Include compelling hooks and attention-grabbing elements":""}
   - Create emotional connection with readers
   - Use vivid examples and case studies
   - Include relatable scenarios and analogies

2. CONTENT STRUCTURE & FLOW:
   - Follow the ${n.contentStructure||"analytical"} structure approach
   - Create logical progression of ideas
   - Use smooth transitions between sections
   - Implement scannable formatting with headings, subheadings, and bullet points
   - Include summary boxes or key takeaways

3. AUTHORITY & CREDIBILITY:
   - Synthesize information from authoritative sources
   - Include specific statistics, data points, and research findings
   - Reference expert opinions and industry insights
   - Maintain factual accuracy with proper attribution
   - Use confident, authoritative language without hedging

4. SEO & OPTIMIZATION:
   ${n.seoOptimization?"- Naturally integrate target keywords and semantic terms":""}
   - Optimize for search intent and user queries
   - Include relevant long-tail keywords
   - Create compelling meta descriptions and headings
   - Structure content for featured snippets

5. ENGAGEMENT & ACTION:
   ${n.includeCallToAction?"- Include strategic calls-to-action throughout":""}
   - Provide actionable insights and practical advice
   - Create interactive elements (questions, challenges, exercises)
   - Encourage reader participation and engagement
   - End with clear next steps

6. PROFESSIONAL STANDARDS:
   - Write at ${n.readabilityTarget||"high-school"} reading level
   - Maintain consistent brand voice and tone
   - Use active voice and strong verbs
   - Eliminate jargon unless necessary (then explain it)
   - Create content that outperforms competitors

7. CONTENT QUALITY:
   - Provide unique insights and original analysis
   - Go beyond surface-level information
   - Include multiple perspectives and viewpoints
   - Address potential objections and counterarguments
   - Create comprehensive, definitive resource

WRITING INSTRUCTIONS:
- Write ONLY in clean, valid markdown format with proper heading hierarchy
- Use # for main title, ## for major sections, ### for subsections
- Use **bold** for emphasis and *italics* for subtle emphasis
- Create bullet points with - (dash) and numbered lists with 1. 2. 3.
- Use proper line breaks between sections (double newline)
- Start with a compelling hook that immediately engages readers
- Use the research context to support all claims and statements
- Create content that demonstrates deep expertise and understanding
- Include relevant quotes, statistics, and data from the sources
- Maintain the specified tone and voice throughout
- Structure content for maximum readability and engagement
- End with a powerful conclusion and clear call-to-action
- IMPORTANT: Return ONLY the markdown content, no additional text or formatting

${n.brandVoice?`BRAND VOICE: ${n.brandVoice}`:""}

Create content that not only informs but also inspires, engages, and drives action. This should be the definitive resource on this topic that readers will bookmark and share.
`;try{let e=await this.gemini.generateContent(o,{temperature:.7,maxOutputTokens:1e4}),t=this.cleanAndValidateMarkdown(e);return this.updateProgress("content-generation","final-output",95,"Professional content generation completed"),t}catch(e){throw console.error("Professional RAG content generation error:",e),Error("Failed to generate professional content")}}async generateContentWithoutRAG(e,t,r,a={}){console.log("\uD83E\uDD16 Generating content without research data using AI knowledge only");let n=`
You are a world-class professional content writer creating comprehensive content about "${e}".

CONTENT BRIEF:
Topic: "${e}"
Content Type: ${a.contentType||"article"}
Target Word Count: ${a.targetWordCount||2e3}
Target Audience: ${a.targetAudience||"general"}
Content Purpose: ${a.contentPurpose||"inform"}
Tone: ${a.tone||"professional"}
Content Structure: ${a.contentStructure||"analytical"}

${t?`CONTENT STRATEGY:
Goal: ${t.contentGoal}
Target Audience: ${t.targetAudience}
Structure: ${t.contentStructure}
Key Messages: ${t.keyMessages.join(", ")}
Hooks: ${t.hooks.join(", ")}
Call to Actions: ${t.callToActions.join(", ")}`:""}

${r?`CONTENT OUTLINE:
Title: ${r.title}
Introduction Hook: ${r.introduction.hook}
Introduction Context: ${r.introduction.context}
Introduction Thesis: ${r.introduction.thesis}
Sections: ${r.sections.map(e=>`${e.heading} (${e.keyPoints.join(", ")})`).join(" | ")}
Conclusion: ${r.conclusion.summary}`:""}

WRITING REQUIREMENTS:
- Write ONLY in clean, valid markdown format with proper heading hierarchy
- Use # for main title, ## for major sections, ### for subsections
- Use **bold** for emphasis and *italics* for subtle emphasis
- Create bullet points with - (dash) and numbered lists with 1. 2. 3.
- Use proper line breaks between sections (double newline)
- Write a comprehensive, authoritative article about ${e}
- Use your knowledge to provide accurate, up-to-date information
- Include practical insights and actionable advice
- Create engaging, professional content that demonstrates expertise
- Include relevant examples, case studies, and best practices
- Write at ${a.readabilityTarget||"high-school"} reading level
- Maintain ${a.tone||"professional"} tone throughout
- IMPORTANT: Return ONLY the markdown content, no additional text or formatting

CONTENT STRUCTURE:
1. Compelling introduction with hook
2. Clear section headings and subheadings using ## and ###
3. Bullet points and numbered lists where appropriate
4. Practical examples and case studies
5. Actionable insights and recommendations
6. Strong conclusion with key takeaways

Create comprehensive, high-quality markdown content that serves as a definitive resource on this topic.
`;try{let e=await this.gemini.generateContent(n,{temperature:.7,maxOutputTokens:8e3}),t=this.cleanAndValidateMarkdown(e);return console.log("✅ Fallback content generation completed"),t}catch(e){throw console.error("Fallback content generation error:",e),Error("Failed to generate content even without research data")}}async executeWorkflow(e,t={}){let r=Date.now();this.phaseTimings={},this.errorCount=0,this.retryCount=0;try{let a,n,i=await this.analyzeTopicWithGemini(e,t);(t.contentPurpose||t.targetAudience||t.contentStructure)&&(a=await this.generateContentStrategy(e,i,t)),a&&(t.contentStructure||t.targetWordCount)&&(n=await this.generateContentOutline(e,i,a,t));let s=await this.conductPrimaryResearch(i,t.maxPrimaryResults||6,t),o=await this.performGapAnalysis(e,i,s),c=await this.conductDeepResearch(o,t.maxDeepResults||4),l={primary:s,deep:c,createdAt:new Date,topic:e,totalSources:s.length+c.length},u=await this.generateContentWithRAG(e,l,a,n,t),d=await this.generateTitle(e,u),h=this.extractHooks(u),p=this.extractCallToActions(u),g=i.competitiveKeywords.concat(i.semanticKeywords),m=this.calculateEnhancedQualityMetrics(u,l,t),y=this.generateCitations(l,u),f=this.analyzeReadability(u,t),T=this.performBasicFactCheck(u,l),w=Date.now();return this.updateProgress("optimization","completed",100,"Professional content workflow completed successfully"),{article:u,title:d,wordCount:u.split(" ").length,qualityScore:this.calculateQualityScore(u,l),sourcesUsed:l.totalSources,knowledgeBase:l,gapAnalysis:o,qualityMetrics:m,citations:y,contentStrategy:a,outline:n,hooks:h,callToActions:p,seoKeywords:g,readabilityAnalysis:f,factCheckResults:T,metadata:{totalResearchTime:w-r,sourcesAnalyzed:l.totalSources,keywordsTargeted:i.keyTerms,confidenceScore:this.calculateConfidenceScore(o,l),phaseTimings:this.phaseTimings,errorCount:this.errorCount,retryCount:this.retryCount,averageSourceQuality:this.calculateAverageSourceQuality(l),contentOptimizationScore:this.calculateContentOptimizationScore(u,t),engagementPrediction:this.predictEngagement(u,t),competitiveAdvantage:this.assessCompetitiveAdvantage(u,i),brandAlignment:this.assessBrandAlignment(u,t)}}}catch(t){console.error("Professional content workflow error:",t);let e=t instanceof Error?t.message:"Unknown error";throw Error(`Professional workflow execution failed: ${e}`)}}calculateQualityMetrics(e,t){let r=e.split(" ").length/e.split(/[.!?]+/).filter(e=>e.trim().length>0).length;return{readabilityScore:this.calculateReadabilityScore(r,e),coherenceScore:this.calculateCoherenceScore(e),factualAccuracy:this.calculateFactualAccuracy(e,t),sourceReliability:this.calculateSourceReliability(t),comprehensiveness:this.calculateComprehensiveness(e,t),originalityScore:this.calculateOriginalityScore(e,t),engagementScore:75,storytellingQuality:70,hookEffectiveness:80,callToActionStrength:75,seoOptimization:85,brandVoiceConsistency:75,expertiseLevel:80,trustworthiness:85,emotionalResonance:70,actionability:75,visualAppeal:80,scanability:85}}calculateReadabilityScore(e,t){let r=100;return e>25?r-=20:e>20&&(r-=10),Math.max(0,Math.min(100,r-=30*((t.match(/\b\w{8,}\b/g)||[]).length/t.split(" ").length)))}calculateCoherenceScore(e){let t=50;return e.includes("##")&&(t+=15),e.includes("###")&&(t+=10),e.match(/\b(however|therefore|furthermore|moreover|consequently)\b/gi)&&(t+=10),e.match(/\b(first|second|third|finally|in conclusion)\b/gi)&&(t+=10),(e.includes("- ")||e.match(/\d+\./))&&(t+=5),Math.min(100,t)}calculateFactualAccuracy(e,t){let r=this.calculateAverageSourceQuality(t),a=e.match(/\d+%|\d+\.\d+%|\$\d+|\d+,\d+/g)||[],n=e.match(/according to|research shows|study found/gi)||[],i=.6*r;return i+=Math.min(20,2*a.length),Math.min(100,i+=Math.min(20,3*n.length))}calculateSourceReliability(e){let t=[...e.primary,...e.deep];if(0===t.length)return 0;let r=t.reduce((e,t)=>e+(t.metadata.authorityScore||0),0)/t.length;return Math.min(100,r+5*t.filter(e=>"official"===e.metadata.sourceType).length+3*t.filter(e=>"academic"===e.metadata.sourceType).length)}calculateComprehensiveness(e,t){let r=e.split(" ").length,a=t.totalSources,n=0;return r>=2e3?n+=30:r>=1500?n+=25:r>=1e3?n+=20:n+=10,n+=Math.min(30,3*a),n+=Math.min(20,3*(e.match(/#{1,6}/g)||[]).length),Math.min(100,n+=Math.min(20,2*new Set([...t.primary,...t.deep].map(e=>e.metadata.domain)).size))}calculateOriginalityScore(e,t){let r;r=70+Math.min(15,2*(e.match(/\b(analysis|insight|implication|conclusion|recommendation)\b/gi)||[]).length),e.includes("##")&&(r+=5),(e.includes("- ")||e.match(/\d+\./))&&(r+=5);let a=[...t.primary,...t.deep].map(e=>e.content.substring(0,500)).join(" "),n=e.toLowerCase().split(" "),i=a.toLowerCase().split(" ");return Math.max(0,Math.min(100,r-=30*(n.filter(e=>e.length>4&&i.includes(e)).length/n.length)))}generateCitations(e,t){return[...e.primary,...e.deep].filter(e=>e.relevanceScore>60).slice(0,10).map(e=>({url:e.url,title:e.title,domain:e.metadata.domain,relevanceScore:e.relevanceScore,usedInSections:this.identifyUsedSections(e,t)}))}identifyUsedSections(e,t){let r=t.split(/#{1,6}/).filter(e=>e.trim().length>0),a=e.metadata.keyTopics;return r.map((e,t)=>a.some(t=>e.toLowerCase().includes(t.toLowerCase()))?`Section ${t+1}`:null).filter(Boolean)}calculateAverageSourceQuality(e){let t=[...e.primary,...e.deep];return 0===t.length?0:t.reduce((e,t)=>e+(.4*t.relevanceScore+.3*(t.metadata.authorityScore||0)+.2*(t.metadata.credibilityScore||0)+.1*(t.metadata.freshness||0)),0)/t.length}calculateRelevanceScore(e,t){let r=e.toLowerCase(),a=0;return t.forEach(e=>{let t=e.toLowerCase(),n=(r.match(RegExp(t,"g"))||[]).length;a+=10*n}),Math.min(a,100)}extractKeyTopics(e,t){let r=e.toLowerCase();return t.filter(e=>r.includes(e.toLowerCase()))}removeDuplicateResearch(e){let t=new Set;return e.filter(e=>{let r=e.url;return!t.has(r)&&(t.add(r),!0)})}async generateTitle(e,t){let r=`
Create a compelling, SEO-optimized title for this article about "${e}".

Article content preview:
${t.substring(0,500)}...

Requirements:
- 50-60 characters ideal length
- Engaging and click-worthy
- Includes main keyword naturally
- Professional tone
- Captures the essence of the comprehensive content

Return only the title, nothing else.
`;try{return(await this.gemini.generateContent(r,{temperature:.6,maxOutputTokens:100})).trim()}catch(t){return`Complete Guide to ${e}`}}calculateQualityScore(e,t){let r=0,a=e.split(" ").length;a>=2e3?r+=25:a>=1500?r+=20:a>=1e3?r+=15:r+=10,r+=Math.min(3*new Set([...t.primary,...t.deep].map(e=>e.metadata.domain)).size,25);let n=e.includes("##"),i=e.includes("###");n&&(r+=10),i&&(r+=10),(e.includes("- ")||e.includes("1. "))&&(r+=5);let s=t.totalSources;return s>=10?r+=25:s>=5?r+=20:r+=3*s,Math.min(r,100)}calculateConfidenceScore(e,t){let r=e.coverageScore;return r+=Math.min(2*t.totalSources,20),Math.max(Math.min(r-=10*e.gaps.filter(e=>"high"===e.importance).length,100),0)}extractHooks(e){let t=[],r=e.split(/[.!?]+/).filter(e=>e.trim().length>10);return r.length>0&&t.push(r[0].trim()),t.push(...(e.match(/^[^.!?]*\?/gm)||[]).slice(0,2)),t.push(...(e.match(/^\d+%[^.!?]*[.!?]/gm)||[]).slice(0,2)),t.slice(0,5)}extractCallToActions(e){let t=[];return[/\b(start|begin|try|get|download|subscribe|sign up|learn more|contact|call|visit)[^.!?]*[.!?]/gi,/\b(take action|get started|join|discover|explore)[^.!?]*[.!?]/gi].forEach(r=>{let a=e.match(r)||[];t.push(...a.slice(0,2))}),t.slice(0,5)}calculateEnhancedQualityMetrics(e,t,r){return{...this.calculateQualityMetrics(e,t),engagementScore:this.calculateEngagementScore(e,r),storytellingQuality:this.calculateStorytellingQuality(e,r),hookEffectiveness:this.calculateHookEffectiveness(e),callToActionStrength:this.calculateCTAStrength(e),seoOptimization:this.calculateSEOOptimization(e,r),brandVoiceConsistency:this.calculateBrandVoiceConsistency(e,r),expertiseLevel:this.calculateExpertiseLevel(e,t),trustworthiness:this.calculateTrustworthiness(e,t),emotionalResonance:this.calculateEmotionalResonance(e),actionability:this.calculateActionability(e),visualAppeal:this.calculateVisualAppeal(e),scanability:this.calculateScanability(e)}}calculateEngagementScore(e,t){let r=50;return e.includes("?")&&(r+=10),e.match(/\b(you|your)\b/gi)&&(r+=10),e.includes("!")&&(r+=5),e.match(/\b(imagine|picture|consider)\b/gi)&&(r+=10),Math.min(r,100)}calculateStorytellingQuality(e,t){let r=30;return t.enableStorytelling&&(e.match(/\b(once|when|after|before|during)\b/gi)&&(r+=15),e.match(/\b(story|example|case|experience)\b/gi)&&(r+=15),e.match(/\b(result|outcome|conclusion|lesson)\b/gi)&&(r+=10)),Math.min(r,100)}calculateHookEffectiveness(e){let t=40,r=e.split(/[.!?]/)[0];return r&&(r.includes("?")&&(t+=20),r.match(/\d+%/)&&(t+=20),r.match(/\b(imagine|what if|did you know)\b/gi)&&(t+=15)),Math.min(t,100)}calculateCTAStrength(e){let t;return t=30+Math.min(5*(e.match(/\b(start|begin|try|get|download|subscribe|learn|contact|visit|join|discover)\b/gi)||[]).length,30),e.match(/\b(get started|sign up|learn more|contact us|try now)\b/gi)&&(t+=20),Math.min(t,100)}calculateSEOOptimization(e,t){let r=40;return t.seoOptimization&&(e.includes("##")&&(r+=15),e.includes("###")&&(r+=10),e.match(/\b\w+\b/g)?.length&&e.match(/\b\w+\b/g).length>300&&(r+=15),e.includes("- ")&&(r+=10)),Math.min(r,100)}calculateBrandVoiceConsistency(e,t){let r=70;return t.brandVoice&&(r=75),r}calculateExpertiseLevel(e,t){let r;return Math.min(r=50+Math.min(2*(e.match(/\b(research|study|analysis|data|evidence|proven|expert|professional)\b/gi)||[]).length,30)+.2*this.calculateAverageSourceQuality(t),100)}calculateTrustworthiness(e,t){let r;return Math.min(r=50+Math.min(5*(e.match(/\b(according to|research shows|study found|data indicates)\b/gi)||[]).length,25)+.25*this.calculateSourceReliability(t),100)}calculateEmotionalResonance(e){let t;return Math.min(t=40+Math.min(3*(e.match(/\b(amazing|incredible|powerful|transform|breakthrough|success|achieve|overcome)\b/gi)||[]).length,30)+Math.min(.5*(e.match(/\b(you|your|we|us|our)\b/gi)||[]).length,20),100)}calculateActionability(e){let t;return Math.min(t=40+Math.min(3*(e.match(/\b(step|how to|method|process|technique|strategy|tip|guide)\b/gi)||[]).length,30)+Math.min(2*(e.match(/\d+\./g)||[]).length,20),100)}calculateVisualAppeal(e){let t=50;return e.includes("##")&&(t+=15),e.includes("###")&&(t+=10),e.includes("- ")&&(t+=10),e.includes("**")&&(t+=10),e.includes("`")&&(t+=5),Math.min(t,100)}calculateScanability(e){let t=40,r=e.split("\n\n").filter(e=>e.trim().length>0);return r.reduce((e,t)=>e+t.length,0)/r.length<500&&(t+=20),e.includes("- ")&&(t+=15),e.includes("##")&&(t+=15),e.includes("###")&&(t+=10),Math.min(t,100)}analyzeReadability(e,t){let r=e.split(" ").length,a=r/e.split(/[.!?]+/).filter(e=>e.trim().length>0).length,n=100,i="High School",s="moderate";return a>25?(n-=30,i="College",s="complex"):a>20?(n-=15,i="High School"):(i="Middle School",s="simple"),{grade:i,score:Math.max(n,0),readingTime:Math.ceil(r/200),complexity:s}}performBasicFactCheck(e,t){let r=e.match(/\b\d+%|\$\d+|\d+,\d+|\b(research shows|study found|according to)\b/gi)||[],a=Math.floor(.7*r.length),n=r.length-a,i=(this.calculateSourceReliability(t)+a/r.length*100)/2;return{verifiedClaims:a,unverifiedClaims:n,disputedClaims:0,overallTrustworthiness:Math.min(i,100)}}calculateContentOptimizationScore(e,t){let r=50;return t.seoOptimization&&(r+=15),r+=.2*this.analyzeReadability(e,t).score,e.includes("##")&&(r+=10),e.includes("- ")&&(r+=10),Math.min(r,100)}predictEngagement(e,t){let r;return Math.min(50+.3*this.calculateEngagementScore(e,t)+.2*this.calculateHookEffectiveness(e)+.2*this.calculateEmotionalResonance(e)+.3*this.calculateActionability(e),100)}assessCompetitiveAdvantage(e,t){let r=60,a=e.split(" ").length;return a>2e3?r+=15:a>1500&&(r+=10),r+=Math.min(2*(e.match(/\b(insight|analysis|conclusion|implication|recommendation)\b/gi)||[]).length,20),Math.min(r+=5*t.contentGaps.filter(t=>e.toLowerCase().includes(t.toLowerCase())).length,100)}assessBrandAlignment(e,t){let r=70;return t.brandVoice&&(r=80),"professional"===(t.tone||"professional")&&e.match(/\b(professional|expert|industry|business)\b/gi)&&(r+=10),Math.min(r,100)}parseTopicAnalysis(e){let t=t=>{let r=RegExp(`===${t}===\\s*([\\s\\S]*?)(?====|$)`,"i"),a=e.match(r);return a?a[1].trim():""},r=e=>t(e).split("\n").map(e=>e.replace(/^-\s*/,"").trim()).filter(e=>e.length>0);return{mainTopic:t("MAIN_TOPIC"),subtopics:r("SUBTOPICS"),keyTerms:r("KEY_TERMS"),searchQueries:r("SEARCH_QUERIES"),complexity:t("COMPLEXITY"),estimatedSources:parseInt(t("ESTIMATED_SOURCES"))||10,audienceIntent:t("AUDIENCE_INTENT"),contentAngle:t("CONTENT_ANGLE"),competitiveKeywords:r("COMPETITIVE_KEYWORDS"),trendingTopics:r("TRENDING_TOPICS"),contentGaps:r("CONTENT_GAPS"),userQuestions:r("USER_QUESTIONS"),semanticKeywords:r("SEMANTIC_KEYWORDS")}}parseContentStrategy(e){let t=t=>{let r=RegExp(`===${t}===\\s*([\\s\\S]*?)(?====|$)`,"i"),a=e.match(r);return a?a[1].trim():""},r=e=>t(e).split("\n").map(e=>e.replace(/^-\s*/,"").trim()).filter(e=>e.length>0),a=e=>{let r=t(e).split("\n").filter(e=>e.trim()),a={};return r.forEach(e=>{let[t,...r]=e.split(":");if(t&&r.length>0){let e=r.join(":").trim();e.startsWith("[")&&e.endsWith("]")?a[t.trim()]=e.slice(1,-1).split(",").map(e=>e.trim()):a[t.trim()]=e}}),a};return{contentGoal:t("CONTENT_GOAL"),targetAudience:t("TARGET_AUDIENCE"),contentStructure:t("CONTENT_STRUCTURE"),keyMessages:r("KEY_MESSAGES"),hooks:r("HOOKS"),callToActions:r("CALL_TO_ACTIONS"),seoStrategy:a("SEO_STRATEGY"),narrativeStructure:a("NARRATIVE_STRUCTURE")}}parseContentOutline(e){let t=t=>{let r=RegExp(`===${t}===\\s*([\\s\\S]*?)(?====|$)`,"i"),a=e.match(r);return a?a[1].trim():""},r=e=>{let t=e.split("\n").filter(e=>e.trim()),r={};return t.forEach(e=>{let[t,...a]=e.split(":");t&&a.length>0&&(r[t.trim().toLowerCase()]=a.join(":").trim())}),r};return{title:t("TITLE"),introduction:r(t("INTRODUCTION")),sections:t("SECTIONS").split(/SECTION\s*\d+/i).filter(e=>e.trim()).map(e=>{let t=e.split("\n").filter(e=>e.trim()),r={heading:"",subheadings:[],keyPoints:[],supportingData:[],sources:[]},a="";return t.forEach(e=>{let t=e.trim();t.startsWith("Heading:")?r.heading=t.replace("Heading:","").trim():t.startsWith("Subheadings:")?a="subheadings":t.startsWith("Key Points:")?a="keyPoints":t.startsWith("Supporting Data:")?a="supportingData":t.startsWith("Sources:")?a="sources":t.startsWith("-")&&a&&r[a].push(t.replace(/^-\s*/,""))}),r}),conclusion:r(t("CONCLUSION")),metadata:{estimatedWordCount:parseInt(t("ESTIMATED_WORD_COUNT"))||2e3,readingTime:parseInt(t("READING_TIME"))||10,difficultyLevel:t("DIFFICULTY_LEVEL")||"intermediate"}}}parseGapAnalysis(e){let t=t=>{let r=RegExp(`===${t}===\\s*([\\s\\S]*?)(?====|$)`,"i"),a=e.match(r);return a?a[1].trim():""};return{gaps:t("GAPS").split(/GAP\s*\d+/i).filter(e=>e.trim()).map(e=>{let t=e.split("\n").filter(e=>e.trim()),r={missingTopic:"",importance:"medium",suggestedQueries:[],reason:""};return t.forEach(e=>{let t=e.trim();t.startsWith("Topic:")?r.missingTopic=t.replace("Topic:","").trim():t.startsWith("Importance:")?r.importance=t.replace("Importance:","").trim():t.startsWith("Reason:")?r.reason=t.replace("Reason:","").trim():t.startsWith("Queries:")&&(r.suggestedQueries=t.replace("Queries:","").split(",").map(e=>e.trim()))}),r}),coverageScore:parseInt(t("COVERAGE_SCORE"))||70,needsDeepResearch:"true"===t("NEEDS_DEEP_RESEARCH").toLowerCase(),priorityTopics:t("PRIORITY_TOPICS").split("\n").map(e=>e.replace(/^-\s*/,"").trim()).filter(e=>e.length>0)}}parseQueryStrategy(e){let t=t=>{let r=RegExp(`===${t}===\\s*([\\s\\S]*?)(?====|$)`,"i"),a=e.match(r);return a?a[1].trim():""},r=e=>t(e).split("\n").map(e=>e.replace(/^-\s*/,"").trim()).filter(e=>e.length>0),a=[{type:"broad-exploratory",queries:r("BROAD_EXPLORATORY_QUERIES"),resultsPerQuery:3,strategy:"broad-first"},{type:"specific-factual",queries:r("SPECIFIC_FACTUAL_QUERIES"),resultsPerQuery:4,strategy:"fact-focused"},{type:"comparative-analysis",queries:r("COMPARATIVE_ANALYSIS_QUERIES"),resultsPerQuery:3,strategy:"comparative"},{type:"expert-perspective",queries:r("EXPERT_PERSPECTIVE_QUERIES"),resultsPerQuery:2,strategy:"authority-focused"},{type:"recent-developments",queries:r("RECENT_DEVELOPMENTS_QUERIES"),resultsPerQuery:3,strategy:"temporal-focused"}].filter(e=>e.queries.length>0),n=a.reduce((e,t)=>e+t.queries.length,0);return{queryGroups:a,totalQueries:n,estimatedTime:2*n}}createFallbackQueryStrategy(e){let t=e.searchQueries.length>0?e.searchQueries:[e.mainTopic,`${e.mainTopic} guide`,`${e.mainTopic} 2024`];return{queryGroups:[{type:"broad-exploratory",queries:t,resultsPerQuery:5,strategy:"fallback"}],totalQueries:t.length,estimatedTime:2*t.length}}parseRapidAnalysisResult(e){let t=t=>{let r=RegExp(`===${t}===\\s*([\\s\\S]*?)(?====|$)`,"i"),a=e.match(r);return a?a[1].trim():""},r=e=>t(e).split("\n").map(e=>e.replace(/^-\s*/,"").trim()).filter(e=>e.length>0);return{targetedQueries:t("TARGETED_QUERIES").split(/QUERY\s*\d+/i).filter(e=>e.trim()).map(e=>{let t=e.split("\n").filter(e=>e.trim()),r={query:"",type:"content-gap",strategy:"",priority:"medium",reasoning:""};return t.forEach(e=>{let t=e.trim();t.startsWith("Query:")?r.query=t.replace("Query:","").trim():t.startsWith("Type:")?r.type=t.replace("Type:","").trim():t.startsWith("Strategy:")?r.strategy=t.replace("Strategy:","").trim():t.startsWith("Priority:")?r.priority=t.replace("Priority:","").trim():t.startsWith("Reasoning:")&&(r.reasoning=t.replace("Reasoning:","").trim())}),r}).filter(e=>e.query.length>0),contentPatterns:r("CONTENT_PATTERNS"),keyInsights:r("KEY_INSIGHTS"),missingAspects:r("MISSING_ASPECTS")}}cleanAndValidateMarkdown(e){if(!e)return"";let t=e.trim();t=(t=(t=(t=(t=t.replace(/^(Here's|Here is|The article|The content|Below is).*?:\s*/i,"")).replace(/^```markdown\s*/i,"")).replace(/\s*```\s*$/i,"")).replace(/^#{7,}/gm,"######")).replace(/^(#{1,6})\s*(.+)$/gm,"$1 $2").replace(/^(\s*)-\s*(.+)$/gm,"$1- $2").replace(/^(\s*)\d+\.\s*(.+)$/gm,"$1$2").replace(/\*{3,}/g,"**").replace(/_{3,}/g,"__").replace(/\n{4,}/g,"\n\n\n").replace(/\n{2,}(#{1,6})/g,"\n\n$1");let r=/^#{1,6}\s+.+$/m.test(t),a=t.length>100;if(!r&&a){let e=t.split("\n"),r=e[0];r&&!r.startsWith("#")&&(e[0]=`# ${r}`,t=e.join("\n"))}return t=t.replace(/^\s+|\s+$/g,"").replace(/\n{3,}/g,"\n\n"),console.log("\uD83E\uDDF9 Cleaned and validated markdown content"),t}}async function d(e){try{let{topic:t,options:r={}}=await e.json();if(!t)return o.NextResponse.json({error:"Topic is required"},{status:400});let a=new TextEncoder,n=new ReadableStream({async start(e){try{let n=new u(t=>{try{let r={type:"progress",...t},n=`data: ${JSON.stringify(r)}

`;e.enqueue(a.encode(n))}catch(e){console.error("Error sending progress update:",e)}}),i=await n.executeWorkflow(t,r);try{let t={type:"result",article:i.article,title:i.title,wordCount:i.wordCount,qualityScore:i.qualityScore,sourcesUsed:i.sourcesUsed,hooks:i.hooks,callToActions:i.callToActions,seoKeywords:i.seoKeywords,readabilityAnalysis:i.readabilityAnalysis,factCheckResults:i.factCheckResults,metadata:{totalResearchTime:i.metadata.totalResearchTime,sourcesAnalyzed:i.metadata.sourcesAnalyzed,keywordsTargeted:i.metadata.keywordsTargeted,confidenceScore:i.metadata.confidenceScore,errorCount:i.metadata.errorCount,retryCount:i.metadata.retryCount},qualityMetrics:{readabilityScore:i.qualityMetrics.readabilityScore,coherenceScore:i.qualityMetrics.coherenceScore,factualAccuracy:i.qualityMetrics.factualAccuracy,sourceReliability:i.qualityMetrics.sourceReliability,comprehensiveness:i.qualityMetrics.comprehensiveness,originalityScore:i.qualityMetrics.originalityScore},citations:i.citations.slice(0,10)},r=`data: ${JSON.stringify(t)}

`;e.enqueue(a.encode(r))}catch(r){console.error("Error serializing result:",r);let t=`data: ${JSON.stringify({type:"result",article:i.article,title:i.title,wordCount:i.wordCount,qualityScore:i.qualityScore,sourcesUsed:i.sourcesUsed})}

`;e.enqueue(a.encode(t))}e.close()}catch(r){console.error("Superagent workflow error:",r);let t=r instanceof Error?r.message:"Workflow execution failed";try{let r=`data: ${JSON.stringify({type:"error",message:t})}

`;e.enqueue(a.encode(r))}catch(e){console.error("Error sending error message:",e)}e.close()}}});return new Response(n,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"}})}catch(e){return console.error("Superagent API error:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let{topic:t,options:r={}}=await e.json();if(!t)return o.NextResponse.json({error:"Topic is required"},{status:400});let a=new u,n=await a.executeWorkflow(t,r);return o.NextResponse.json({success:!0,data:n})}catch(t){console.error("Superagent workflow error:",t);let e=t instanceof Error?t.message:"Workflow execution failed";return o.NextResponse.json({success:!1,error:e},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/superagent/route",pathname:"/api/superagent",filename:"route",bundlePath:"app/api/superagent/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/superagent/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:y}=p;function f(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},38522:e=>{"use strict";e.exports=require("node:zlib")},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93356:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});let a=new(r(37449)).ij("AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY");class n{constructor(e="gemini-2.0-flash-lite"){this.model=a.getGenerativeModel({model:e})}async generateContent(e,t={}){try{let r={temperature:t.temperature||.7,maxOutputTokens:t.maxOutputTokens||4e3,topP:t.topP||.95,topK:t.topK||40},a=await this.model.generateContent({contents:[{role:"user",parts:[{text:e}]}],generationConfig:r});return(await a.response).text()}catch(e){throw console.error("Gemini generation error:",e),Error("Failed to generate content with Gemini")}}async generateBlogPost(e,t,r,a,n){let i=`
You are a world-class professional content writer and subject matter expert. Create a comprehensive, engaging blog post about "${e}".

CONTENT REQUIREMENTS:
- Target word count: ${t} words
- Tone: ${r}
- Format: Professional markdown with proper headings, lists, and structure
- Include compelling hook and engaging introduction
- Use narrative storytelling and real-world examples
- Include strategic call-to-action at the end
- Write as a primary authoritative source
- Use confident, authoritative language (avoid hedging)

PROFESSIONAL WRITING STANDARDS:
- Start with an attention-grabbing hook (question, statistic, or bold statement)
- Create emotional connection with readers through storytelling
- Use scannable formatting with headings, subheadings, and bullet points
- Include actionable insights and practical advice
- Incorporate relevant statistics and data points
- Use active voice and strong verbs
- Create smooth transitions between sections
- End with a powerful conclusion and clear next steps

${n?.title?`Article Title: ${n.title}
`:""}
${n?.targetKeyword?`Target Keyword: ${n.targetKeyword} (use naturally throughout the content)
`:""}
${n?.targetAudience?`Target Audience: ${n.targetAudience} (tailor content for this audience)
`:""}
${n?.competitors?`Competitors to outperform: ${n.competitors} (create content that surpasses these sources)
`:""}

${a?`Research Data to incorporate:
${a}
`:""}

CONTENT STRUCTURE:
1. Compelling Hook (question, statistic, or bold statement)
2. Introduction with context and thesis
3. Main sections with clear headings and subheadings
4. Practical examples and case studies
5. Actionable takeaways and recommendations
6. Powerful conclusion with call-to-action

Create content that not only informs but also inspires action and provides exceptional value to readers. This should be the definitive resource on this topic.
`;return this.generateContent(i,{temperature:.7,maxOutputTokens:8e3})}async generateEmail(e,t,r,a){let n=`
Create a professional email for the following:

Purpose: ${e}
Target Audience: ${t}
Tone: ${r}
Key Points to Include: ${a.join(", ")}

Requirements:
- Include compelling subject line
- Professional email structure (greeting, body, closing)
- Clear call-to-action
- Appropriate tone and language for the audience
- Concise but comprehensive

Format the response as:
Subject: [Subject Line]

[Email Body]
`;return this.generateContent(n,{temperature:.6,maxOutputTokens:1500})}async generateTweet(e,t,r=!0){let a=`
Create an engaging Twitter/X tweet about "${e}".

Style: ${t}
Include hashtags: ${r}

Requirements:
- Maximum 280 characters
- Engaging and shareable
- Include relevant emojis if appropriate
- ${r?"Include 2-3 relevant hashtags":"No hashtags"}
- Hook the reader's attention
- Encourage engagement (likes, retweets, replies)

Create a tweet that stands out in the feed and drives engagement.
`;return this.generateContent(a,{temperature:.8,maxOutputTokens:500})}async extractKeywords(e){let t=`
Extract the most important keywords from this topic for Google search: "${e}"

Requirements:
- If the topic is a single word or simple phrase, use it as the main keyword
- For complex topics, extract 3-5 key terms that best represent the topic
- Focus on the main concepts and important terms
- Use words that would be effective for Google search
- Return only the keywords separated by spaces, nothing else
- Do not include common words like "the", "and", "of", etc.
- Do not add words like "meaning", "definition", "example" unless they are part of the original topic
- Focus on specific, searchable terms from the original topic

Examples:
Topic: "magistral"
Keywords: magistral

Topic: "How to build a React application with TypeScript"
Keywords: React TypeScript application build development

Topic: "artificial intelligence in healthcare"
Keywords: artificial intelligence healthcare

Return only the keywords:
`;return this.generateContent(t,{temperature:.1,maxOutputTokens:50})}async generateYouTubeScript(e,t,r,a){let n=`
Create a YouTube video script about "${e}".

Video Duration: ${t}
Style: ${r}
Target Audience: ${a}

Requirements:
- Include compelling hook in first 15 seconds
- Clear structure with timestamps
- Engaging storytelling throughout
- Include call-to-action for likes, subscribes, comments
- Natural speaking rhythm and flow
- Include cues for visuals/graphics where appropriate
- End with strong conclusion and next video teaser

Format:
[HOOK - 0:00-0:15]
[INTRODUCTION - 0:15-0:45]
[MAIN CONTENT - Sections with timestamps]
[CONCLUSION & CTA - Final section]

Create a script that keeps viewers engaged throughout the entire video.
`;return this.generateContent(n,{temperature:.7,maxOutputTokens:5e3})}async extractKeywordsFromContent(e){let t=`
Analyze this content and extract the most important SEO keywords and phrases that would be valuable for content optimization:

Content:
${e.substring(0,3e3)}

Rules:
- Extract 8-12 high-value keywords and phrases
- Focus on terms that appear frequently and seem important
- Include both single keywords and 2-3 word phrases
- Prioritize terms that would be good for SEO targeting
- Separate keywords with commas
- Don't include common words like "the", "and", "or", etc.

Return only the keywords separated by commas:
`;return this.generateContent(t,{temperature:.2,maxOutputTokens:200})}}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},99475:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var a=r(94612),n=r(68281);class i{constructor(){this.apiKey="AIzaSyBlQ7HhWbY37GYbeV9ZJZmTUucspF2KbXE",this.searchEngineId="830840f1a0eaf4acf"}async search(e,t=10){try{if(console.log(`🔍 Searching for: ${e}`),!this.apiKey||!this.searchEngineId)throw console.error("❌ Google Search API credentials not configured"),Error("Google Search API credentials not configured");let r=await a.A.get("https://www.googleapis.com/customsearch/v1",{params:{key:this.apiKey,cx:this.searchEngineId,q:e,num:Math.min(t,10)},timeout:15e3}),n=r.data.items?.map(e=>({title:e.title,link:e.link,snippet:e.snippet,displayLink:e.displayLink}))||[];return console.log(`📊 Found ${n.length} results`),0===n.length?(console.log(`⚠️ No results found for query: "${e}"`),console.log(`📊 Total results available: ${r.data.searchInformation?.totalResults||"0"}`)):n.forEach((e,t)=>{console.log(`${t+1}. ${e.link}`)}),{items:n,searchInformation:{totalResults:r.data.searchInformation?.totalResults||"0",searchTime:r.data.searchInformation?.searchTime||0}}}catch(e){throw console.error("Google Search API error:",e.response?.data||e.message),e.response?.status===403?console.error("❌ API key invalid or quota exceeded"):e.response?.status===400&&console.error("❌ Invalid search parameters"),Error(`Failed to perform search: ${e.response?.data?.error?.message||e.message}`)}}async extractContent(e){try{console.log(`📄 Extracting content from: ${e}`);let t=await a.A.get(e,{timeout:1e4,headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}}),r=n.Hh(t.data);r("script, style, nav, header, footer, aside, .advertisement, .ads, .social-share").remove();let i="";for(let e of["article",".content",".post-content",".entry-content",".article-content","main",".main-content","#content",".post-body",".article-body"]){let t=r(e);t.length>0&&t.text().trim().length>i.length&&(i=t.text().trim())}return i||(i=r("body").text().trim()),i=i.replace(/\s+/g," ").replace(/\n\s*\n/g,"\n").trim(),console.log(`✅ Extracted ${i.length} characters from ${e}`),i}catch(t){return console.error(`❌ Failed to extract content from ${e}:`,t),""}}async searchAndExtract(e,t=5){try{let r=await this.search(e,t),a=r.items.map(async e=>({url:e.link,content:await this.extractContent(e.link)})),n=(await Promise.all(a)).filter(e=>e.content.length>100);return console.log(`📚 Successfully extracted content from ${n.length}/${r.items.length} URLs`),{searchResults:r.items,extractedContent:n}}catch(e){throw console.error("Search and extract error:",e),Error("Failed to search and extract content")}}formatResearchData(e){return e.map((e,t)=>{let r=e.content.length>2e3?e.content.substring(0,2e3)+"...":e.content;return`=== SOURCE ${t+1}: ${e.url} ===
${r}
`}).join("\n")}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,580,830,772],()=>r(13767));module.exports=a})();