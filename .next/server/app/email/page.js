(()=>{var e={};e.id=469,e.ids=[469],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7857:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var s=a(60687),r=a(43210),i=a(97905),l=a(41550),n=a(56085),o=a(96474),c=a(11860),d=a(70615),p=a(81822),m=a(79216),u=a(37924),h=a(60884),x=a(53559),y=a(16337);function b(){let[e,t]=(0,r.useState)({purpose:"",audience:"",tone:"professional",keyPoints:[]}),[a,b]=(0,r.useState)(""),[v,g]=(0,r.useState)(!1),[f,j]=(0,r.useState)(""),[w,N]=(0,r.useState)(""),k=async t=>{t.preventDefault(),g(!0),N(""),j("");try{let t=await fetch("/api/generate/email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await t.json();if(!t.ok)throw Error(a.error||"Failed to generate email");j(a.content)}catch(e){N(e instanceof Error?e.message:"An error occurred")}finally{g(!1)}},A=()=>{a.trim()&&!e.keyPoints.includes(a.trim())&&(t({...e,keyPoints:[...e.keyPoints,a.trim()]}),b(""))},P=a=>{t({...e,keyPoints:e.keyPoints.filter((e,t)=>t!==a)})},C=async()=>{try{await navigator.clipboard.writeText(f)}catch(e){console.error("Failed to copy:",e)}};return(0,s.jsx)(y.A,{children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsx)(i.P.div,{className:"mb-8",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,s.jsx)("div",{className:"p-3 rounded-2xl bg-gradient-to-r from-pink-500 to-rose-500",children:(0,s.jsx)(l.A,{className:"w-8 h-8 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-2",children:"Email Generator"}),(0,s.jsx)("p",{className:"text-xl text-white/80",children:"Craft compelling emails for marketing, outreach, and professional communication"})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsx)(i.P.div,{className:"lg:col-span-1",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.6},children:(0,s.jsx)(m.A,{variant:"elevated",children:(0,s.jsxs)("form",{onSubmit:k,className:"space-y-6",children:[(0,s.jsx)(u.A,{label:"Email Purpose",type:"textarea",value:e.purpose,onChange:a=>t({...e,purpose:a}),placeholder:"e.g., Product launch announcement, Follow-up after meeting",required:!0,rows:3,icon:(0,s.jsx)(n.A,{className:"w-5 h-5"})}),(0,s.jsx)(u.A,{label:"Target Audience",type:"text",value:e.audience,onChange:a=>t({...e,audience:a}),placeholder:"e.g., Existing customers, Potential clients, Team members",required:!0}),(0,s.jsx)(u.A,{label:"Tone",type:"select",value:e.tone,onChange:a=>t({...e,tone:a}),options:[{value:"professional",label:"Professional"},{value:"friendly",label:"Friendly"},{value:"formal",label:"Formal"},{value:"casual",label:"Casual"},{value:"persuasive",label:"Persuasive"},{value:"urgent",label:"Urgent"}]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Key Points to Include"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("input",{type:"text",value:a,onChange:e=>b(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),A())},placeholder:"Add a key point",className:"flex-1 px-4 py-2 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-blue-400"}),(0,s.jsx)(p.A,{type:"button",variant:"secondary",size:"sm",onClick:A,disabled:!a.trim(),icon:(0,s.jsx)(o.A,{className:"w-4 h-4"}),children:"Add"})]}),e.keyPoints.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:e.keyPoints.map((e,t)=>(0,s.jsxs)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"flex items-center gap-2 px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full text-sm border border-blue-500/30",children:[(0,s.jsx)("span",{children:e}),(0,s.jsx)("button",{type:"button",onClick:()=>P(t),className:"hover:bg-blue-500/30 rounded-full p-0.5 transition-colors",children:(0,s.jsx)(c.A,{className:"w-3 h-3"})})]},t))})]}),v&&(0,s.jsx)(x.A,{value:75,label:"Generating email...",variant:"animated"}),(0,s.jsx)(p.A,{type:"submit",variant:"primary",size:"lg",disabled:v||!e.purpose||!e.audience,loading:v,className:"w-full",icon:(0,s.jsx)(l.A,{className:"w-5 h-5"}),children:v?"Generating Email...":"Generate Email"})]})})}),(0,s.jsxs)(i.P.div,{className:"lg:col-span-2",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},children:[(0,s.jsx)(h.A,{type:"error",message:w,show:!!w,onClose:()=>N(""),autoClose:!0}),f?(0,s.jsxs)(m.A,{variant:"glass",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white",children:"Generated Email"}),(0,s.jsx)(p.A,{variant:"secondary",size:"sm",onClick:C,icon:(0,s.jsx)(d.A,{className:"w-4 h-4"}),className:"tooltip-modern","data-tooltip":"Copy to clipboard",children:"Copy"})]}),(0,s.jsx)("div",{className:"max-h-[70vh] overflow-auto p-6 bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20",children:(0,s.jsx)("pre",{className:"text-white whitespace-pre-wrap font-sans leading-relaxed",children:f})})]}):v?null:(0,s.jsxs)(m.A,{variant:"glass",className:"text-center py-16",children:[(0,s.jsx)(l.A,{className:"w-16 h-16 text-white/40 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white/80 mb-2",children:"Your generated email will appear here"}),(0,s.jsx)("p",{className:"text-white/60",children:'Fill out the form and click "Generate Email" to get started'})]})]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28204:(e,t,a)=>{Promise.resolve().then(a.bind(a,72667))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34284:(e,t,a)=>{Promise.resolve().then(a.bind(a,7857))},35071:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37924:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var s=a(60687),r=a(97905),i=a(43210);function l({label:e,type:t="text",value:a,onChange:l,placeholder:n="",required:o=!1,disabled:c=!1,error:d,icon:p,options:m=[],rows:u=4,className:h=""}){let[x,y]=(0,i.useState)(!1),b=e=>{l("number"===t?Number(e.target.value):e.target.value)},v=`
    modern-input-field
    ${d?"border-red-400":""}
    ${c?"opacity-50 cursor-not-allowed":""}
    ${h}
  `.trim(),g=`
    modern-input-label
    modern-input-label-active
    ${d?"text-red-400":""}
  `.trim();return(0,s.jsxs)(r.P.div,{className:"modern-input",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,s.jsxs)("label",{className:g,children:[e," ",o&&(0,s.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[p&&(0,s.jsx)("div",{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 z-10 pointer-events-none",children:p}),"textarea"===t?(0,s.jsx)("textarea",{value:a,onChange:b,onFocus:()=>y(!0),onBlur:()=>y(!1),placeholder:n,required:o,disabled:c,rows:u,className:`${v} ${p?"pl-14":""}`}):"select"===t?(0,s.jsxs)("select",{value:a,onChange:b,onFocus:()=>y(!0),onBlur:()=>y(!1),required:o,disabled:c,className:`${v} ${p?"pl-14":""}`,children:[(0,s.jsx)("option",{value:"",children:n||`Select ${e}`}),m.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,s.jsx)("input",{type:t,value:a,onChange:b,onFocus:()=>y(!0),onBlur:()=>y(!1),placeholder:n,required:o,disabled:c,className:`${v} ${p?"pl-14":""}`})]}),d&&(0,s.jsx)(r.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"text-red-400 text-sm mt-1 px-3",children:d})]})}},47102:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=a(65239),r=a(48088),i=a(88170),l=a.n(i),n=a(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);a.d(t,o);let c={children:["",{children:["email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,72667)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/email/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/email/page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/email/page",pathname:"/email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53559:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var s=a(60687),r=a(97905);function i({value:e,max:t=100,label:a,showPercentage:i=!0,variant:l="default",size:n="md",className:o=""}){let c=Math.min(e/t*100,100);return(0,s.jsxs)("div",{className:`w-full ${o}`,children:[(a||i)&&(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[a&&(0,s.jsx)("span",{className:"text-sm font-medium text-white/80",children:a}),i&&(0,s.jsxs)("span",{className:"text-sm font-medium text-white/60",children:[Math.round(c),"%"]})]}),(0,s.jsx)("div",{className:`progress-modern ${{sm:"h-2",md:"h-3",lg:"h-4"}[n]}`,children:(0,s.jsx)(r.P.div,{className:`progress-modern-bar ${{default:"bg-gradient-to-r from-blue-500 to-purple-600",gradient:"bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500",animated:"bg-gradient-to-r from-blue-500 to-purple-600 animate-pulse"}[l]}`,initial:{width:0},animate:{width:`${c}%`},transition:{duration:.8,ease:"easeOut"}})})]})}},60884:(e,t,a)=>{"use strict";a.d(t,{A:()=>m});var s=a(60687),r=a(88920),i=a(97905),l=a(5336),n=a(35071),o=a(62688);let c=(0,o.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),d=(0,o.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var p=a(11860);function m({type:e,title:t,message:a,show:o,onClose:m,autoClose:u=!1,duration:h=5e3,icon:x}){let y={success:(0,s.jsx)(l.A,{className:"w-5 h-5"}),error:(0,s.jsx)(n.A,{className:"w-5 h-5"}),warning:(0,s.jsx)(c,{className:"w-5 h-5"}),info:(0,s.jsx)(d,{className:"w-5 h-5"})};return u&&o&&m&&setTimeout(()=>{m()},h),(0,s.jsx)(r.N,{children:o&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{duration:.3},className:`alert-modern ${{success:"alert-modern-success",error:"alert-modern-error",warning:"alert-modern-warning",info:"alert-modern-info"}[e]}`,children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:x||y[e]}),(0,s.jsxs)("div",{className:"flex-1",children:[t&&(0,s.jsx)("h4",{className:"font-semibold mb-1",children:t}),(0,s.jsx)("p",{className:"text-sm opacity-90",children:a})]}),m&&(0,s.jsx)("button",{onClick:m,className:"flex-shrink-0 ml-4 p-1 rounded-lg hover:bg-white/10 transition-colors",children:(0,s.jsx)(p.A,{className:"w-4 h-4"})})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70615:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},72667:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/email/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/email/page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[447,135,901,971,141,556],()=>a(47102));module.exports=s})();