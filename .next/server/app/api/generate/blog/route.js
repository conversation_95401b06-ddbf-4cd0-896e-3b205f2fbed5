(()=>{var e={};e.id=498,e.ids=[498],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>h});var o={};r.r(o),r.d(o,{POST:()=>u});var s=r(96559),a=r(48088),n=r(37719),i=r(32190),c=r(93356),l=r(99475);async function u(e){try{let{topic:t,wordCount:r,tone:o,includeResearch:s,targetKeyword:a,competitors:n,targetAudience:u,title:d}=await e.json();if(!t)return i.NextResponse.json({error:"Topic is required"},{status:400});let p=new c.p,h="";if(s)try{console.log("\uD83D\uDD0D Starting research phase...");let e=new l.J,r=await p.extractKeywords(t);console.log(`🎯 Extracted keywords: ${r.trim()}`);let{extractedContent:o}=await e.searchAndExtract(r.trim(),5);o.length>0?(h=e.formatResearchData(o),console.log(`📚 Research completed: ${h.length} characters of research data`)):console.log("⚠️ No research data extracted, proceeding without research")}catch(e){console.error("Research phase failed:",e),console.log("\uD83D\uDCDD Proceeding with content generation without research data")}console.log("✍️ Starting content generation...");let g=await p.generateBlogPost(t,r||1e3,o||"professional",h,{targetKeyword:a,competitors:n,targetAudience:u,title:d});return console.log("✅ Blog post generated successfully"),i.NextResponse.json({success:!0,content:g,researchUsed:!!h,researchDataLength:h.length})}catch(e){return console.error("Blog generation error:",e),i.NextResponse.json({error:"Failed to generate blog post"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/generate/blog/route",pathname:"/api/generate/blog",filename:"route",bundlePath:"app/api/generate/blog/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/generate/blog/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:h,serverHooks:g}=d;function m(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:h})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},38522:e=>{"use strict";e.exports=require("node:zlib")},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93356:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});let o=new(r(37449)).ij("AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY");class s{constructor(e="gemini-2.0-flash-lite"){this.model=o.getGenerativeModel({model:e})}async generateContent(e,t={}){try{let r={temperature:t.temperature||.7,maxOutputTokens:t.maxOutputTokens||4e3,topP:t.topP||.95,topK:t.topK||40},o=await this.model.generateContent({contents:[{role:"user",parts:[{text:e}]}],generationConfig:r});return(await o.response).text()}catch(e){throw console.error("Gemini generation error:",e),Error("Failed to generate content with Gemini")}}async generateBlogPost(e,t,r,o,s){let a=`
You are a world-class professional content writer and subject matter expert. Create a comprehensive, engaging blog post about "${e}".

CONTENT REQUIREMENTS:
- Target word count: ${t} words
- Tone: ${r}
- Format: Professional markdown with proper headings, lists, and structure
- Include compelling hook and engaging introduction
- Use narrative storytelling and real-world examples
- Include strategic call-to-action at the end
- Write as a primary authoritative source
- Use confident, authoritative language (avoid hedging)

PROFESSIONAL WRITING STANDARDS:
- Start with an attention-grabbing hook (question, statistic, or bold statement)
- Create emotional connection with readers through storytelling
- Use scannable formatting with headings, subheadings, and bullet points
- Include actionable insights and practical advice
- Incorporate relevant statistics and data points
- Use active voice and strong verbs
- Create smooth transitions between sections
- End with a powerful conclusion and clear next steps

${s?.title?`Article Title: ${s.title}
`:""}
${s?.targetKeyword?`Target Keyword: ${s.targetKeyword} (use naturally throughout the content)
`:""}
${s?.targetAudience?`Target Audience: ${s.targetAudience} (tailor content for this audience)
`:""}
${s?.competitors?`Competitors to outperform: ${s.competitors} (create content that surpasses these sources)
`:""}

${o?`Research Data to incorporate:
${o}
`:""}

CONTENT STRUCTURE:
1. Compelling Hook (question, statistic, or bold statement)
2. Introduction with context and thesis
3. Main sections with clear headings and subheadings
4. Practical examples and case studies
5. Actionable takeaways and recommendations
6. Powerful conclusion with call-to-action

Create content that not only informs but also inspires action and provides exceptional value to readers. This should be the definitive resource on this topic.
`;return this.generateContent(a,{temperature:.7,maxOutputTokens:8e3})}async generateEmail(e,t,r,o){let s=`
Create a professional email for the following:

Purpose: ${e}
Target Audience: ${t}
Tone: ${r}
Key Points to Include: ${o.join(", ")}

Requirements:
- Include compelling subject line
- Professional email structure (greeting, body, closing)
- Clear call-to-action
- Appropriate tone and language for the audience
- Concise but comprehensive

Format the response as:
Subject: [Subject Line]

[Email Body]
`;return this.generateContent(s,{temperature:.6,maxOutputTokens:1500})}async generateTweet(e,t,r=!0){let o=`
Create an engaging Twitter/X tweet about "${e}".

Style: ${t}
Include hashtags: ${r}

Requirements:
- Maximum 280 characters
- Engaging and shareable
- Include relevant emojis if appropriate
- ${r?"Include 2-3 relevant hashtags":"No hashtags"}
- Hook the reader's attention
- Encourage engagement (likes, retweets, replies)

Create a tweet that stands out in the feed and drives engagement.
`;return this.generateContent(o,{temperature:.8,maxOutputTokens:500})}async extractKeywords(e){let t=`
Extract the most important keywords from this topic for Google search: "${e}"

Requirements:
- If the topic is a single word or simple phrase, use it as the main keyword
- For complex topics, extract 3-5 key terms that best represent the topic
- Focus on the main concepts and important terms
- Use words that would be effective for Google search
- Return only the keywords separated by spaces, nothing else
- Do not include common words like "the", "and", "of", etc.
- Do not add words like "meaning", "definition", "example" unless they are part of the original topic
- Focus on specific, searchable terms from the original topic

Examples:
Topic: "magistral"
Keywords: magistral

Topic: "How to build a React application with TypeScript"
Keywords: React TypeScript application build development

Topic: "artificial intelligence in healthcare"
Keywords: artificial intelligence healthcare

Return only the keywords:
`;return this.generateContent(t,{temperature:.1,maxOutputTokens:50})}async generateYouTubeScript(e,t,r,o){let s=`
Create a YouTube video script about "${e}".

Video Duration: ${t}
Style: ${r}
Target Audience: ${o}

Requirements:
- Include compelling hook in first 15 seconds
- Clear structure with timestamps
- Engaging storytelling throughout
- Include call-to-action for likes, subscribes, comments
- Natural speaking rhythm and flow
- Include cues for visuals/graphics where appropriate
- End with strong conclusion and next video teaser

Format:
[HOOK - 0:00-0:15]
[INTRODUCTION - 0:15-0:45]
[MAIN CONTENT - Sections with timestamps]
[CONCLUSION & CTA - Final section]

Create a script that keeps viewers engaged throughout the entire video.
`;return this.generateContent(s,{temperature:.7,maxOutputTokens:5e3})}async extractKeywordsFromContent(e){let t=`
Analyze this content and extract the most important SEO keywords and phrases that would be valuable for content optimization:

Content:
${e.substring(0,3e3)}

Rules:
- Extract 8-12 high-value keywords and phrases
- Focus on terms that appear frequently and seem important
- Include both single keywords and 2-3 word phrases
- Prioritize terms that would be good for SEO targeting
- Separate keywords with commas
- Don't include common words like "the", "and", "or", etc.

Return only the keywords separated by commas:
`;return this.generateContent(t,{temperature:.2,maxOutputTokens:200})}}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},99475:(e,t,r)=>{"use strict";r.d(t,{J:()=>a});var o=r(94612),s=r(68281);class a{constructor(){this.apiKey="AIzaSyBlQ7HhWbY37GYbeV9ZJZmTUucspF2KbXE",this.searchEngineId="830840f1a0eaf4acf"}async search(e,t=10){try{if(console.log(`🔍 Searching for: ${e}`),!this.apiKey||!this.searchEngineId)throw console.error("❌ Google Search API credentials not configured"),Error("Google Search API credentials not configured");let r=await o.A.get("https://www.googleapis.com/customsearch/v1",{params:{key:this.apiKey,cx:this.searchEngineId,q:e,num:Math.min(t,10)},timeout:15e3}),s=r.data.items?.map(e=>({title:e.title,link:e.link,snippet:e.snippet,displayLink:e.displayLink}))||[];return console.log(`📊 Found ${s.length} results`),0===s.length?(console.log(`⚠️ No results found for query: "${e}"`),console.log(`📊 Total results available: ${r.data.searchInformation?.totalResults||"0"}`)):s.forEach((e,t)=>{console.log(`${t+1}. ${e.link}`)}),{items:s,searchInformation:{totalResults:r.data.searchInformation?.totalResults||"0",searchTime:r.data.searchInformation?.searchTime||0}}}catch(e){throw console.error("Google Search API error:",e.response?.data||e.message),e.response?.status===403?console.error("❌ API key invalid or quota exceeded"):e.response?.status===400&&console.error("❌ Invalid search parameters"),Error(`Failed to perform search: ${e.response?.data?.error?.message||e.message}`)}}async extractContent(e){try{console.log(`📄 Extracting content from: ${e}`);let t=await o.A.get(e,{timeout:1e4,headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}}),r=s.Hh(t.data);r("script, style, nav, header, footer, aside, .advertisement, .ads, .social-share").remove();let a="";for(let e of["article",".content",".post-content",".entry-content",".article-content","main",".main-content","#content",".post-body",".article-body"]){let t=r(e);t.length>0&&t.text().trim().length>a.length&&(a=t.text().trim())}return a||(a=r("body").text().trim()),a=a.replace(/\s+/g," ").replace(/\n\s*\n/g,"\n").trim(),console.log(`✅ Extracted ${a.length} characters from ${e}`),a}catch(t){return console.error(`❌ Failed to extract content from ${e}:`,t),""}}async searchAndExtract(e,t=5){try{let r=await this.search(e,t),o=r.items.map(async e=>({url:e.link,content:await this.extractContent(e.link)})),s=(await Promise.all(o)).filter(e=>e.content.length>100);return console.log(`📚 Successfully extracted content from ${s.length}/${r.items.length} URLs`),{searchResults:r.items,extractedContent:s}}catch(e){throw console.error("Search and extract error:",e),Error("Failed to search and extract content")}}formatResearchData(e){return e.map((e,t)=>{let r=e.content.length>2e3?e.content.substring(0,2e3)+"...":e.content;return`=== SOURCE ${t+1}: ${e.url} ===
${r}
`}).join("\n")}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,580,830,772],()=>r(1934));module.exports=o})();