"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[483],{19910:(e,t,s)=>{s.d(t,{A:()=>i});var a=s(95155),l=s(1978);function i(e){let{children:t,variant:s="default",hover:i=!0,className:r="",onClick:n}=e,o="\n    ".concat({default:"card-modern",elevated:"card-modern-elevated",glass:"glass",neu:"card-neu"}[s],"\n    ").concat(n?"cursor-pointer":"","\n    ").concat(r,"\n  ").trim();return(0,a.jsx)(l.P.div,{className:o,onClick:n,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},...i?{whileHover:{y:-4,scale:1.02},transition:{duration:.3}}:{},children:t})}},20408:(e,t,s)=>{s.d(t,{SettingsProvider:()=>o,t:()=>n});var a=s(95155),l=s(12115);let i={firstName:"<PERSON>",lastName:"Doe",email:"<EMAIL>",bio:"Content creator and digital marketer passionate about AI-powered writing.",avatar:"",defaultLanguage:"en",timezone:"America/New_York",defaultWordCount:1e3,defaultTone:"professional",includeResearchByDefault:!0,autoSaveEnabled:!0,emailNotifications:!0,pushNotifications:!1,weeklyReports:!0,marketingEmails:!1,theme:"dark",accentColor:"blue",animationsEnabled:!0,compactMode:!1,profileVisibility:"private",dataSharing:!1,analyticsTracking:!0},r=(0,l.createContext)(void 0),n=()=>{let e=(0,l.useContext)(r);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e},o=e=>{let{children:t}=e,[s,n]=(0,l.useState)(i),[o,c]=(0,l.useState)(!0),[d,h]=(0,l.useState)(null);(0,l.useEffect)(()=>{try{let e=localStorage.getItem("userSettings");if(e){let t=JSON.parse(e);n({...i,...t})}}catch(e){console.error("Failed to load settings:",e),h("Failed to load user settings")}finally{c(!1)}},[]),(0,l.useEffect)(()=>{if(!o)try{localStorage.setItem("userSettings",JSON.stringify(s))}catch(e){console.error("Failed to save settings:",e),h("Failed to save settings")}},[s,o]);let x=async()=>{try{h(null);let e=await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to save settings")}catch(t){let e=t instanceof Error?t.message:"Failed to save settings";throw h(e),Error(e)}};return(0,a.jsx)(r.Provider,{value:{settings:s,updateSettings:e=>{n(t=>({...t,...e})),h(null)},resetSettings:()=>{n(i),h(null)},saveSettings:x,isLoading:o,error:d},children:t})}},39636:(e,t,s)=>{s.d(t,{A:()=>i});var a=s(95155),l=s(1978);function i(e){let{children:t,variant:s="primary",size:i="md",disabled:r=!1,loading:n=!1,onClick:o,type:c="button",className:d="",icon:h}=e,x="\n    ".concat("btn-modern","\n    ").concat({primary:"btn-modern-primary",secondary:"btn-modern-secondary",accent:"btn-modern-accent"}[s],"\n    ").concat({sm:"text-sm",md:"text-base",lg:"text-lg"}[i],"\n    ").concat(r||n?"opacity-50 cursor-not-allowed":"","\n    ").concat(d,"\n  ").trim();return(0,a.jsx)(l.P.button,{type:c,className:x,onClick:r||n?void 0:o,disabled:r||n,whileHover:r||n?{}:{scale:1.02},whileTap:r||n?{}:{scale:.98},transition:{duration:.2},children:n?(0,a.jsx)("div",{className:"loading-spinner"}):(0,a.jsxs)(a.Fragment,{children:[h&&(0,a.jsx)("span",{className:"flex-shrink-0",children:h}),t]})})}},96108:(e,t,s)=>{s.d(t,{A:()=>H});var a=s(95155),l=s(12115),i=s(1978),r=s(6874),n=s.n(r),o=s(35695),c=s(20408),d=s(73783),h=s(53311),x=s(57100),m=s(28883),u=s(18175),b=s(9803),p=s(72713),g=s(57434),f=s(381),w=s(94788),j=s(13052),N=s(42355),v=s(71007),y=s(34835);let A=[{title:"Dashboard",href:"/dashboard",icon:d.A,badge:null},{title:"AI Superagent",href:"/superagent",icon:h.A,badge:"Legacy"},{title:"Content Tools",items:[{title:"Blog Generator",href:"/blog",icon:x.A,badge:"Popular"},{title:"Email Generator",href:"/email",icon:m.A,badge:null},{title:"Tweet Generator",href:"/tweet",icon:u.A,badge:"New"},{title:"YouTube Scripts",href:"/youtube",icon:b.A,badge:null}]},{title:"Analytics",href:"/analytics",icon:p.A,badge:null},{title:"Content Library",href:"/library",icon:g.A,badge:null}],S=[{title:"Settings",href:"/settings",icon:f.A},{title:"Help & Support",href:"/help",icon:w.A}];function k(e){let{isCollapsed:t,onToggle:s}=e,r=(0,o.usePathname)(),[d,x]=(0,l.useState)(null),{settings:m}=(0,c.t)(),u=e=>"/dashboard"===e?"/dashboard"===r:r.startsWith(e),b=e=>{let{item:s,isNested:l=!1}=e,i=u(s.href),r=s.icon,o=d===s.href;return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(n(),{href:s.href,className:"\n            relative flex items-center gap-3 px-3 py-2.5 rounded-xl transition-all duration-200 cursor-pointer\n            ".concat(i?"bg-gradient-to-r from-blue-500/20 to-purple-600/20 text-white border border-blue-500/30":"text-white/70 hover:text-white hover:bg-white/5","\n            ").concat(l?"ml-6":"","\n            ").concat(t?"justify-center":"","\n          "),onMouseEnter:()=>x(s.href),onMouseLeave:()=>x(null),children:[i&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-600/10 rounded-xl"}),(0,a.jsxs)("div",{className:"relative z-10 flex items-center gap-3 w-full",children:[(0,a.jsx)(r,{className:"w-5 h-5 flex-shrink-0 ".concat(i?"text-blue-400":"")}),!t&&(0,a.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,a.jsx)("span",{className:"font-medium",children:s.title}),s.badge&&(0,a.jsx)("span",{className:"\n                    px-2 py-0.5 text-xs font-semibold rounded-full\n                    ".concat("New"===s.badge?"bg-green-500/20 text-green-400 border border-green-500/30":"bg-orange-500/20 text-orange-400 border border-orange-500/30","\n                  "),children:s.badge})]})]})]}),t&&o&&(0,a.jsxs)("div",{className:"absolute left-full ml-4 px-3 py-2 bg-gray-800/95 backdrop-blur-sm text-white text-sm rounded-lg shadow-xl border border-white/20 whitespace-nowrap z-[60]",style:{top:"50%",transform:"translateY(-50%)",pointerEvents:"none"},children:[s.title,s.badge&&(0,a.jsx)("span",{className:"ml-2 px-1.5 py-0.5 text-xs bg-blue-500/20 text-blue-400 rounded",children:s.badge})]})]})};return(0,a.jsxs)(i.P.aside,{animate:{width:t?80:280},transition:{duration:.3,ease:"easeInOut"},className:"fixed left-0 top-0 h-full bg-gray-900/95 backdrop-blur-xl border-r border-white/10 z-40 flex flex-col",onMouseLeave:()=>x(null),children:[(0,a.jsx)("div",{className:"p-4 border-b border-white/10",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[!t&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"w-8 h-8 text-blue-400"}),(0,a.jsx)("span",{className:"text-xl font-bold text-white",children:"Invincible"})]}),(0,a.jsx)("button",{onClick:s,className:"p-2 rounded-lg hover:bg-white/10 transition-colors duration-200 cursor-pointer",children:t?(0,a.jsx)(j.A,{className:"w-5 h-5 text-white/70"}):(0,a.jsx)(N.A,{className:"w-5 h-5 text-white/70"})})]})}),(0,a.jsx)("div",{className:"flex-1 p-4 space-y-2 overflow-y-auto",children:A.map((e,s)=>(0,a.jsx)("div",{className:"space-y-1",children:e.items?(0,a.jsxs)("div",{className:"space-y-1",children:[!t&&(0,a.jsx)("div",{className:"px-3 py-2 text-xs font-semibold text-white/50 uppercase tracking-wider",children:e.title}),e.items.map(e=>(0,a.jsx)(b,{item:e,isNested:!t},e.href))]}):(0,a.jsx)(b,{item:e})},s))}),(0,a.jsxs)("div",{className:"p-4 border-t border-white/10 space-y-2",children:[S.map(e=>(0,a.jsx)(b,{item:e},e.href)),(0,a.jsxs)("div",{className:"\n          flex items-center gap-3 p-3 rounded-xl bg-white/5 border border-white/10\n          ".concat(t?"justify-center":"","\n        "),children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center",children:(0,a.jsx)(v.A,{className:"w-4 h-4 text-white"})}),!t&&(0,a.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-white",children:[m.firstName," ",m.lastName]}),(0,a.jsx)("div",{className:"text-xs text-white/60",children:"Pro Plan"})]}),(0,a.jsx)("button",{className:"p-1 rounded-lg hover:bg-white/10 transition-colors",children:(0,a.jsx)(y.A,{className:"w-4 h-4 text-white/70"})})]})]})]})]})}var P=s(60760),C=s(47924),E=s(84616),T=s(71539),I=s(17951),z=s(23861),O=s(66474),F=s(81586),J=s(39636);function D(){let[e,t]=(0,l.useState)(!1),[s,r]=(0,l.useState)(!1),[n,o]=(0,l.useState)(!1),c=[{id:1,title:"Blog post generated",message:'Your "AI in Healthcare" blog post is ready',time:"2 min ago",unread:!0},{id:2,title:"Usage limit warning",message:"You've used 80% of your monthly credits",time:"1 hour ago",unread:!0},{id:3,title:"New feature available",message:"Try our new YouTube script generator",time:"1 day ago",unread:!1}];return(0,a.jsx)("header",{className:"sticky top-0 z-30 bg-white/5 backdrop-blur-xl border-b border-white/10",children:(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,a.jsx)("div",{className:"flex-1 max-w-xl",children:(0,a.jsxs)(i.P.div,{className:"\n              relative flex items-center transition-all duration-300\n              ".concat(e?"scale-105":"","\n            "),children:[(0,a.jsx)(C.A,{className:"absolute left-3 w-5 h-5 text-white/50"}),(0,a.jsx)("input",{type:"text",placeholder:"Search content, templates, or ask AI...",className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300",onFocus:()=>t(!0),onBlur:()=>t(!1)}),e&&(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},className:"absolute top-full left-0 right-0 mt-2 bg-gray-900/95 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl p-4",children:[(0,a.jsx)("div",{className:"text-sm text-white/60 mb-2",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 rounded-lg hover:bg-white/5 cursor-pointer",children:[(0,a.jsx)(E.A,{className:"w-4 h-4 text-blue-400"}),(0,a.jsx)("span",{className:"text-white",children:"Create new blog post"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 rounded-lg hover:bg-white/5 cursor-pointer",children:[(0,a.jsx)(T.A,{className:"w-4 h-4 text-purple-400"}),(0,a.jsx)("span",{className:"text-white",children:"Generate email campaign"})]})]})]})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(J.A,{variant:"primary",size:"sm",icon:(0,a.jsx)(E.A,{className:"w-4 h-4"}),children:"Create"}),(0,a.jsx)(J.A,{variant:"accent",size:"sm",icon:(0,a.jsx)(I.A,{className:"w-4 h-4"}),className:"bg-gradient-to-r from-yellow-500 to-orange-500",children:"Upgrade"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(i.P.button,{onClick:()=>o(!n),className:"relative p-2 rounded-xl bg-white/10 hover:bg-white/20 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,a.jsx)(z.A,{className:"w-5 h-5 text-white"}),c.some(e=>e.unread)&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-gray-900"})]}),(0,a.jsx)(P.N,{children:n&&(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},className:"absolute top-full right-0 mt-2 w-80 bg-gray-900/95 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl p-4 z-50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Notifications"}),(0,a.jsx)("button",{className:"text-sm text-blue-400 hover:text-blue-300",children:"Mark all read"})]}),(0,a.jsx)("div",{className:"space-y-3 max-h-80 overflow-y-auto",children:c.map(e=>(0,a.jsx)("div",{className:"\n                          p-3 rounded-lg border transition-colors cursor-pointer\n                          ".concat(e.unread?"bg-blue-500/10 border-blue-500/20 hover:bg-blue-500/15":"bg-white/5 border-white/10 hover:bg-white/10","\n                        "),children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-white/70 mt-1",children:e.message}),(0,a.jsx)("span",{className:"text-xs text-white/50 mt-2 block",children:e.time})]}),e.unread&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mt-1"})]})},e.id))})]})})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(i.P.button,{onClick:()=>r(!s),className:"flex items-center gap-2 p-2 rounded-xl bg-white/10 hover:bg-white/20 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center",children:(0,a.jsx)(v.A,{className:"w-4 h-4 text-white"})}),(0,a.jsx)(O.A,{className:"w-4 h-4 text-white/70"})]}),(0,a.jsx)(P.N,{children:s&&(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},className:"absolute top-full right-0 mt-2 w-64 bg-gray-900/95 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl p-4 z-50",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4 pb-4 border-b border-white/10",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center",children:(0,a.jsx)(v.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-white",children:"John Doe"}),(0,a.jsx)("div",{className:"text-xs text-white/60",children:"<EMAIL>"}),(0,a.jsx)("div",{className:"text-xs text-blue-400 font-medium",children:"Pro Plan"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("button",{className:"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-left",children:[(0,a.jsx)(v.A,{className:"w-4 h-4 text-white/70"}),(0,a.jsx)("span",{className:"text-sm text-white",children:"Profile Settings"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-left",children:[(0,a.jsx)(F.A,{className:"w-4 h-4 text-white/70"}),(0,a.jsx)("span",{className:"text-sm text-white",children:"Billing & Usage"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-left",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 text-white/70"}),(0,a.jsx)("span",{className:"text-sm text-white",children:"Preferences"})]}),(0,a.jsx)("hr",{className:"border-white/10 my-2"}),(0,a.jsxs)("button",{className:"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-red-500/20 transition-colors text-left",children:[(0,a.jsx)(y.A,{className:"w-4 h-4 text-red-400"}),(0,a.jsx)("span",{className:"text-sm text-red-400",children:"Sign Out"})]})]})]})})]})]})]})})}function H(e){let{children:t}=e,[s,r]=(0,l.useState)(!1),[n,o]=(0,l.useState)(!1);return((0,l.useEffect)(()=>{o(!0);let e=localStorage.getItem("sidebar-collapsed");e&&r(JSON.parse(e))},[]),(0,l.useEffect)(()=>{n&&localStorage.setItem("sidebar-collapsed",JSON.stringify(s))},[s,n]),n)?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900",children:[(0,a.jsx)(k,{isCollapsed:s,onToggle:()=>{r(!s)}}),(0,a.jsxs)(i.P.div,{animate:{marginLeft:s?80:280},transition:{duration:.3,ease:"easeInOut"},className:"min-h-screen",children:[(0,a.jsx)(D,{}),(0,a.jsx)("main",{className:"p-6",children:(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:t})})]}),(0,a.jsxs)("div",{className:"fixed inset-0 pointer-events-none overflow-hidden -z-10",children:[(0,a.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-float"}),(0,a.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-float",style:{animationDelay:"2s"}}),(0,a.jsx)("div",{className:"absolute top-3/4 left-1/3 w-64 h-64 bg-cyan-500/5 rounded-full blur-3xl animate-float",style:{animationDelay:"4s"}})]})]}):null}}}]);