'use client';

/**
 * KaibanJS Super Agent React Component
 * 
 * This component provides a user interface for the KaibanJS-based super agent system
 * with real-time progress tracking and result display.
 */

import React, { useState, useCallback, useRef } from 'react';
import ModernCard from '@/components/ui/ModernCard';
import ModernButton from '@/components/ui/ModernButton';
import ModernInput from '@/components/ui/ModernInput';
import ModernProgress from '@/components/ui/ModernProgress';
import ModernAlert from '@/components/ui/ModernAlert';
import {
  Play,
  Square,
  Clock,
  Users,
  FileText,
  CheckCircle,
  AlertCircle,
  Loader2,
  Download,
  Copy
} from 'lucide-react';

interface SuperAgentOptions {
  contentType: 'article' | 'blog-post' | 'research-paper' | 'comprehensive-guide';
  targetWordCount: number;
  tone: 'professional' | 'casual' | 'academic' | 'conversational';
  targetAudience: 'beginner' | 'intermediate' | 'expert' | 'general';
  maxPrimaryResults: number;
  maxDeepResults: number;
  enableFactChecking: boolean;
  includeSourceCitations: boolean;
}

interface WorkflowProgress {
  phase: string;
  progress: number;
  message: string;
  timestamp: string;
}

interface WorkflowResult {
  success: boolean;
  topic: string;
  title?: string;
  content?: string;
  wordCount?: number;
  qualityScore?: number;
  sourcesUsed?: number;
  executionTime?: number;
  error?: string;
}

export default function KaibanSuperAgent() {
  // State management
  const [topic, setTopic] = useState('');
  const [options, setOptions] = useState<SuperAgentOptions>({
    contentType: 'article',
    targetWordCount: 2000,
    tone: 'professional',
    targetAudience: 'intermediate',
    maxPrimaryResults: 6,
    maxDeepResults: 4,
    enableFactChecking: true,
    includeSourceCitations: true
  });

  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState<WorkflowProgress | null>(null);
  const [result, setResult] = useState<WorkflowResult | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [estimatedTime, setEstimatedTime] = useState<number>(0);

  const eventSourceRef = useRef<EventSource | null>(null);

  // Handle workflow execution
  const handleStartWorkflow = useCallback(async () => {
    if (!topic.trim()) {
      alert('Please enter a topic');
      return;
    }

    setIsRunning(true);
    setProgress(null);
    setResult(null);
    setLogs([]);

    try {
      setLogs(prev => [...prev, `🚀 Starting KaibanJS Super Agent workflow for: "${topic}"`]);

      // Simulate progress updates
      const phases = [
        'Topic Analysis',
        'Content Strategy',
        'Primary Research',
        'Gap Analysis',
        'Deep Research',
        'Content Generation',
        'Quality Assurance'
      ];

      let currentPhase = 0;
      const progressInterval = setInterval(() => {
        if (currentPhase < phases.length) {
          const progress = Math.round(((currentPhase + 1) / phases.length) * 100);
          setProgress({
            phase: phases[currentPhase],
            progress,
            message: `Processing ${phases[currentPhase]}...`,
            timestamp: new Date().toISOString()
          });
          setLogs(prev => [...prev, `� [${phases[currentPhase]}] Processing... (${progress}%)`]);
          currentPhase++;
        }
      }, 2000);

      // Make API call
      const response = await fetch('/api/kaiban-superagent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic,
          options
        })
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setResult(data.result);
        setLogs(prev => [...prev, `✅ Workflow completed successfully!`]);
      } else {
        setResult({
          success: false,
          topic,
          error: data.error || 'Unknown error occurred'
        });
        setLogs(prev => [...prev, `❌ Error: ${data.error || 'Unknown error occurred'}`]);
      }

    } catch (error) {
      console.error('Error starting workflow:', error);
      setResult({
        success: false,
        topic,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      setLogs(prev => [...prev, `❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`]);
    } finally {
      setIsRunning(false);
    }
  }, [topic, options]);

  // Handle workflow cancellation
  const handleStopWorkflow = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setIsRunning(false);
    setLogs(prev => [...prev, `⏹️ Workflow stopped by user`]);
  }, []);

  // Handle content copy
  const handleCopyContent = useCallback(() => {
    if (result?.content) {
      navigator.clipboard.writeText(result.content);
      setLogs(prev => [...prev, `📋 Content copied to clipboard`]);
    }
  }, [result]);

  // Handle content download
  const handleDownloadContent = useCallback(() => {
    if (result?.content) {
      const blob = new Blob([result.content], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${result.title || 'kaiban-content'}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      setLogs(prev => [...prev, `💾 Content downloaded as markdown file`]);
    }
  }, [result]);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <ModernCard variant="glass">
        <div className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Users className="h-6 w-6 text-blue-400" />
            <h1 className="text-2xl font-bold text-white">KaibanJS Super Agent System</h1>
          </div>
          <p className="text-white/70">
            Advanced multi-agent content creation system powered by KaibanJS framework
          </p>
        </div>
      </ModernCard>

      {/* Configuration */}
      <ModernCard variant="glass">
        <div className="p-6 space-y-6">
          <h2 className="text-xl font-semibold text-white mb-4">Configuration</h2>

          {/* Topic Input */}
          <ModernInput
            label="Topic *"
            type="text"
            value={topic}
            onChange={(value) => setTopic(value as string)}
            placeholder="Enter your content topic..."
            disabled={isRunning}
          />

          {/* Options Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <ModernInput
              label="Content Type"
              type="select"
              value={options.contentType}
              onChange={(value) => setOptions(prev => ({ ...prev, contentType: value as any }))}
              disabled={isRunning}
              options={[
                { value: 'article', label: 'Article' },
                { value: 'blog-post', label: 'Blog Post' },
                { value: 'research-paper', label: 'Research Paper' },
                { value: 'comprehensive-guide', label: 'Comprehensive Guide' }
              ]}
            />

            <ModernInput
              label="Tone"
              type="select"
              value={options.tone}
              onChange={(value) => setOptions(prev => ({ ...prev, tone: value as any }))}
              disabled={isRunning}
              options={[
                { value: 'professional', label: 'Professional' },
                { value: 'casual', label: 'Casual' },
                { value: 'academic', label: 'Academic' },
                { value: 'conversational', label: 'Conversational' }
              ]}
            />

            <ModernInput
              label="Target Audience"
              type="select"
              value={options.targetAudience}
              onChange={(value) => setOptions(prev => ({ ...prev, targetAudience: value as any }))}
              disabled={isRunning}
              options={[
                { value: 'beginner', label: 'Beginner' },
                { value: 'intermediate', label: 'Intermediate' },
                { value: 'expert', label: 'Expert' },
                { value: 'general', label: 'General' }
              ]}
            />

            <ModernInput
              label="Word Count"
              type="number"
              value={options.targetWordCount}
              onChange={(value) => setOptions(prev => ({ ...prev, targetWordCount: parseInt(value as string) || 2000 }))}
              disabled={isRunning}
            />
          </div>

          {/* Advanced Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ModernInput
              label="Max Primary Results"
              type="number"
              value={options.maxPrimaryResults}
              onChange={(value) => setOptions(prev => ({ ...prev, maxPrimaryResults: parseInt(value as string) || 6 }))}
              disabled={isRunning}
            />

            <ModernInput
              label="Max Deep Results"
              type="number"
              value={options.maxDeepResults}
              onChange={(value) => setOptions(prev => ({ ...prev, maxDeepResults: parseInt(value as string) || 4 }))}
              disabled={isRunning}
            />
          </div>

          {/* Control Buttons */}
          <div className="flex gap-4 items-center">
            <ModernButton
              onClick={handleStartWorkflow}
              disabled={isRunning || !topic.trim()}
              loading={isRunning}
              variant="primary"
              icon={isRunning ? <Loader2 className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
            >
              {isRunning ? 'Running...' : 'Start Workflow'}
            </ModernButton>

            {isRunning && (
              <ModernButton
                onClick={handleStopWorkflow}
                variant="secondary"
                icon={<Square className="h-4 w-4" />}
              >
                Stop
              </ModernButton>
            )}

            {estimatedTime > 0 && (
              <div className="flex items-center gap-2 px-3 py-2 bg-white/10 rounded-lg border border-white/20">
                <Clock className="h-4 w-4 text-white/60" />
                <span className="text-sm text-white/80">~{estimatedTime} min</span>
              </div>
            )}
          </div>
        </div>
      </ModernCard>

      {/* Progress */}
      {(progress || isRunning) && (
        <ModernCard variant="glass">
          <div className="p-6">
            <h2 className="text-xl font-semibold text-white mb-4">Progress</h2>
            {progress && (
              <div className="space-y-4">
                <ModernProgress
                  value={progress.progress}
                  label={progress.phase}
                  showPercentage={true}
                  variant="gradient"
                />
                <p className="text-sm text-white/70">{progress.message}</p>
              </div>
            )}
          </div>
        </ModernCard>
      )}

      {/* Results */}
      {result && (
        <ModernCard variant="glass">
          <div className="p-6">
            <div className="flex items-center gap-2 mb-6">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-400" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-400" />
              )}
              <h2 className="text-xl font-semibold text-white">Results</h2>
            </div>
            {result.success ? (
              <div className="space-y-6">
                {/* Metadata */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-white/5 rounded-lg border border-white/10">
                    <div className="text-2xl font-bold text-blue-400">{result.wordCount || 0}</div>
                    <div className="text-sm text-white/60">Words</div>
                  </div>
                  <div className="text-center p-4 bg-white/5 rounded-lg border border-white/10">
                    <div className="text-2xl font-bold text-green-400">{result.qualityScore || 0}</div>
                    <div className="text-sm text-white/60">Quality Score</div>
                  </div>
                  <div className="text-center p-4 bg-white/5 rounded-lg border border-white/10">
                    <div className="text-2xl font-bold text-purple-400">{result.sourcesUsed || 0}</div>
                    <div className="text-sm text-white/60">Sources</div>
                  </div>
                  <div className="text-center p-4 bg-white/5 rounded-lg border border-white/10">
                    <div className="text-2xl font-bold text-orange-400">
                      {result.executionTime ? Math.round(result.executionTime / 1000 / 60) : 0}
                    </div>
                    <div className="text-sm text-white/60">Minutes</div>
                  </div>
                </div>

                <div className="border-t border-white/10"></div>

                {/* Content Actions */}
                <div className="flex gap-4">
                  <ModernButton
                    onClick={handleCopyContent}
                    variant="secondary"
                    icon={<Copy className="h-4 w-4" />}
                  >
                    Copy Content
                  </ModernButton>
                  <ModernButton
                    onClick={handleDownloadContent}
                    variant="secondary"
                    icon={<Download className="h-4 w-4" />}
                  >
                    Download
                  </ModernButton>
                </div>

                {/* Generated Content */}
                {result.content && (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">{result.title}</h3>
                    <ModernInput
                      label=""
                      type="textarea"
                      value={result.content}
                      onChange={() => {}} // Read-only
                      disabled={true}
                      rows={20}
                      className="font-mono text-sm"
                    />
                  </div>
                )}
              </div>
            ) : (
              <div className="text-red-400">
                <p>Error: {result.error}</p>
              </div>
            )}
          </div>
        </ModernCard>
      )}

      {/* Logs */}
      {logs.length > 0 && (
        <ModernCard variant="glass">
          <div className="p-6">
            <h2 className="text-xl font-semibold text-white mb-4">Workflow Logs</h2>
            <div className="bg-black/20 p-4 rounded-lg max-h-60 overflow-y-auto border border-white/10">
              {logs.map((log, index) => (
                <div key={index} className="text-sm font-mono mb-1 text-white/80">
                  {log}
                </div>
              ))}
            </div>
          </div>
        </ModernCard>
      )}
    </div>
  );
}
