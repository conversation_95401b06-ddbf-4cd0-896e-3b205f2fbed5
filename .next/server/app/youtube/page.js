(()=>{var e={};e.id=804,e.ids=[804],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},15836:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=a(65239),s=a(48088),i=a(88170),l=a.n(i),n=a(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);a.d(t,o);let d={children:["",{children:["youtube",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,23822)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/youtube/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/youtube/page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/youtube/page",pathname:"/youtube",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23822:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/youtube/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/youtube/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},32853:(e,t,a)=>{Promise.resolve().then(a.bind(a,67084))},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37924:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(60687),s=a(97905),i=a(43210);function l({label:e,type:t="text",value:a,onChange:l,placeholder:n="",required:o=!1,disabled:d=!1,error:c,icon:u,options:p=[],rows:m=4,className:h=""}){let[x,b]=(0,i.useState)(!1),v=e=>{l("number"===t?Number(e.target.value):e.target.value)},y=`
    modern-input-field
    ${c?"border-red-400":""}
    ${d?"opacity-50 cursor-not-allowed":""}
    ${h}
  `.trim(),g=`
    modern-input-label
    modern-input-label-active
    ${c?"text-red-400":""}
  `.trim();return(0,r.jsxs)(s.P.div,{className:"modern-input",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,r.jsxs)("label",{className:g,children:[e," ",o&&(0,r.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[u&&(0,r.jsx)("div",{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 z-10 pointer-events-none",children:u}),"textarea"===t?(0,r.jsx)("textarea",{value:a,onChange:v,onFocus:()=>b(!0),onBlur:()=>b(!1),placeholder:n,required:o,disabled:d,rows:m,className:`${y} ${u?"pl-14":""}`}):"select"===t?(0,r.jsxs)("select",{value:a,onChange:v,onFocus:()=>b(!0),onBlur:()=>b(!1),required:o,disabled:d,className:`${y} ${u?"pl-14":""}`,children:[(0,r.jsx)("option",{value:"",children:n||`Select ${e}`}),p.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,r.jsx)("input",{type:t,value:a,onChange:v,onFocus:()=>b(!0),onBlur:()=>b(!1),placeholder:n,required:o,disabled:d,className:`${y} ${u?"pl-14":""}`})]}),c&&(0,r.jsx)(s.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"text-red-400 text-sm mt-1 px-3",children:c})]})}},42581:(e,t,a)=>{Promise.resolve().then(a.bind(a,23822))},48730:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53559:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(60687),s=a(97905);function i({value:e,max:t=100,label:a,showPercentage:i=!0,variant:l="default",size:n="md",className:o=""}){let d=Math.min(e/t*100,100);return(0,r.jsxs)("div",{className:`w-full ${o}`,children:[(a||i)&&(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[a&&(0,r.jsx)("span",{className:"text-sm font-medium text-white/80",children:a}),i&&(0,r.jsxs)("span",{className:"text-sm font-medium text-white/60",children:[Math.round(d),"%"]})]}),(0,r.jsx)("div",{className:`progress-modern ${{sm:"h-2",md:"h-3",lg:"h-4"}[n]}`,children:(0,r.jsx)(s.P.div,{className:`progress-modern-bar ${{default:"bg-gradient-to-r from-blue-500 to-purple-600",gradient:"bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500",animated:"bg-gradient-to-r from-blue-500 to-purple-600 animate-pulse"}[l]}`,initial:{width:0},animate:{width:`${d}%`},transition:{duration:.8,ease:"easeOut"}})})]})}},60884:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var r=a(60687),s=a(88920),i=a(97905),l=a(5336),n=a(35071),o=a(62688);let d=(0,o.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),c=(0,o.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var u=a(11860);function p({type:e,title:t,message:a,show:o,onClose:p,autoClose:m=!1,duration:h=5e3,icon:x}){let b={success:(0,r.jsx)(l.A,{className:"w-5 h-5"}),error:(0,r.jsx)(n.A,{className:"w-5 h-5"}),warning:(0,r.jsx)(d,{className:"w-5 h-5"}),info:(0,r.jsx)(c,{className:"w-5 h-5"})};return m&&o&&p&&setTimeout(()=>{p()},h),(0,r.jsx)(s.N,{children:o&&(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{duration:.3},className:`alert-modern ${{success:"alert-modern-success",error:"alert-modern-error",warning:"alert-modern-warning",info:"alert-modern-info"}[e]}`,children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:x||b[e]}),(0,r.jsxs)("div",{className:"flex-1",children:[t&&(0,r.jsx)("h4",{className:"font-semibold mb-1",children:t}),(0,r.jsx)("p",{className:"text-sm opacity-90",children:a})]}),p&&(0,r.jsx)("button",{onClick:p,className:"flex-shrink-0 ml-4 p-1 rounded-lg hover:bg-white/10 transition-colors",children:(0,r.jsx)(u.A,{className:"w-4 h-4"})})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67084:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var r=a(60687),s=a(43210),i=a(97905),l=a(2943),n=a(56085),o=a(70615),d=a(31158),c=a(48730),u=a(81822),p=a(79216),m=a(37924),h=a(60884),x=a(53559),b=a(16337);function v(){let[e,t]=(0,s.useState)({topic:"",duration:"5-10 minutes",style:"educational",targetAudience:"general audience"}),[a,v]=(0,s.useState)(!1),[y,g]=(0,s.useState)(""),[j,f]=(0,s.useState)(""),w=async t=>{t.preventDefault(),v(!0),f(""),g("");try{let t=await fetch("/api/generate/youtube",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await t.json();if(!t.ok)throw Error(a.error||"Failed to generate YouTube script");g(a.content)}catch(e){f(e instanceof Error?e.message:"An error occurred")}finally{v(!1)}},N=async()=>{try{await navigator.clipboard.writeText(y)}catch(e){console.error("Failed to copy:",e)}},A=()=>y.split(/\s+/).filter(e=>e.length>0).length;return(0,r.jsx)(b.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsx)(i.P.div,{className:"mb-8",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,r.jsx)("div",{className:"p-3 rounded-2xl bg-gradient-to-r from-orange-500 to-red-500",children:(0,r.jsx)(l.A,{className:"w-8 h-8 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-2",children:"YouTube Script Generator"}),(0,r.jsx)("p",{className:"text-xl text-white/80",children:"Create engaging video scripts with hooks, storytelling, and clear structure"})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)(i.P.div,{className:"lg:col-span-1",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.6},children:(0,r.jsx)(p.A,{variant:"elevated",children:(0,r.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,r.jsx)(m.A,{label:"Video Topic",type:"textarea",value:e.topic,onChange:a=>t({...e,topic:a}),placeholder:"e.g., How to Start a YouTube Channel in 2024",required:!0,rows:3,icon:(0,r.jsx)(n.A,{className:"w-5 h-5"})}),(0,r.jsx)(m.A,{label:"Video Duration",type:"select",value:e.duration,onChange:a=>t({...e,duration:a}),options:[{value:"1-3 minutes",label:"1-3 minutes (Short)"},{value:"3-5 minutes",label:"3-5 minutes"},{value:"5-10 minutes",label:"5-10 minutes"},{value:"10-15 minutes",label:"10-15 minutes"},{value:"15-20 minutes",label:"15-20 minutes"},{value:"20+ minutes",label:"20+ minutes (Long-form)"}]}),(0,r.jsx)(m.A,{label:"Video Style",type:"select",value:e.style,onChange:a=>t({...e,style:a}),options:[{value:"educational",label:"Educational"},{value:"entertaining",label:"Entertaining"},{value:"tutorial",label:"Tutorial"},{value:"review",label:"Review"},{value:"vlog",label:"Vlog"},{value:"documentary",label:"Documentary"},{value:"interview",label:"Interview"},{value:"presentation",label:"Presentation"}]}),(0,r.jsx)(m.A,{label:"Target Audience",type:"text",value:e.targetAudience,onChange:a=>t({...e,targetAudience:a}),placeholder:"e.g., Beginner content creators, Tech enthusiasts"}),a&&(0,r.jsx)(x.A,{value:75,label:"Generating script...",variant:"animated"}),(0,r.jsx)(u.A,{type:"submit",variant:"primary",size:"lg",disabled:a||!e.topic,loading:a,className:"w-full",icon:(0,r.jsx)(l.A,{className:"w-5 h-5"}),children:a?"Generating Script...":"Generate YouTube Script"})]})})}),(0,r.jsxs)(i.P.div,{className:"lg:col-span-2",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},children:[(0,r.jsx)(h.A,{type:"error",message:j,show:!!j,onClose:()=>f(""),autoClose:!0}),y?(0,r.jsxs)(p.A,{variant:"glass",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-white",children:"Generated YouTube Script"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(u.A,{variant:"secondary",size:"sm",onClick:N,icon:(0,r.jsx)(o.A,{className:"w-4 h-4"}),className:"tooltip-modern","data-tooltip":"Copy to clipboard",children:"Copy"}),(0,r.jsx)(u.A,{variant:"secondary",size:"sm",onClick:()=>{let t=new Blob([y],{type:"text/plain"}),a=URL.createObjectURL(t),r=document.createElement("a");r.href=a,r.download=`youtube-script-${e.topic.replace(/\s+/g,"-").toLowerCase()}.txt`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(a)},icon:(0,r.jsx)(d.A,{className:"w-4 h-4"}),className:"tooltip-modern","data-tooltip":"Download script",children:"Download"})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full text-sm border border-blue-500/30",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:["~",Math.ceil(A()/150)," min read"]})]}),(0,r.jsxs)("div",{className:"px-3 py-1 bg-green-500/20 text-green-400 rounded-full text-sm border border-green-500/30",children:[A()," words"]}),(0,r.jsx)("div",{className:"px-3 py-1 bg-purple-500/20 text-purple-400 rounded-full text-sm border border-purple-500/30",children:e.duration})]}),(0,r.jsx)("div",{className:"max-h-[70vh] overflow-auto p-6 bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20",children:(0,r.jsx)("pre",{className:"text-white whitespace-pre-wrap font-sans leading-relaxed",children:y})})]}):a?null:(0,r.jsxs)(p.A,{variant:"glass",className:"text-center py-16",children:[(0,r.jsx)(l.A,{className:"w-16 h-16 text-white/40 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-white/80 mb-2",children:"Your generated YouTube script will appear here"}),(0,r.jsx)("p",{className:"text-white/60",children:'Fill out the form and click "Generate YouTube Script" to get started'})]})]})]})]})})}},70615:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,135,901,971,141,556],()=>a(15836));module.exports=r})();