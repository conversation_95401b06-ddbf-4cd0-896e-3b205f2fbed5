/**
 * OpenRouter Service for Qwen3-235B Model Integration
 * Specialized for thinking and reasoning tasks
 */

export interface OpenRouterConfig {
  apiKey: string;
  baseURL?: string;
  model?: string;
  maxRetries?: number;
  timeout?: number;
}

export interface OpenRouterMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface OpenRouterResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface ThinkingTaskOptions {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  enableThinking?: boolean;
  thinkingDepth?: 'shallow' | 'medium' | 'deep';
}

export class OpenRouterService {
  private config: OpenRouterConfig;
  private defaultModel = 'qwen/qwen3-235b-a22b-04-28';

  constructor(config?: Partial<OpenRouterConfig>) {
    this.config = {
      apiKey: process.env.OPENROUTER_API_KEY || '',
      baseURL: 'https://openrouter.ai/api/v1',
      model: config?.model || this.defaultModel,
      maxRetries: config?.maxRetries || 3,
      timeout: config?.timeout || 60000,
      ...config
    };

    if (!this.config.apiKey) {
      throw new Error('OpenRouter API key is required. Set OPENROUTER_API_KEY environment variable.');
    }
  }

  /**
   * Generate content for thinking tasks using Qwen3-235B
   */
  async generateThinkingContent(
    prompt: string, 
    options: ThinkingTaskOptions = {}
  ): Promise<string> {
    const messages: OpenRouterMessage[] = [
      {
        role: 'system',
        content: this.buildThinkingSystemPrompt(options.thinkingDepth || 'medium')
      },
      {
        role: 'user',
        content: this.enhancePromptForThinking(prompt, options)
      }
    ];

    const response = await this.makeRequest(messages, {
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 4000,
      top_p: options.topP || 0.9,
      frequency_penalty: options.frequencyPenalty || 0.1,
      presence_penalty: options.presencePenalty || 0.1
    });

    return response.choices[0]?.message?.content || '';
  }

  /**
   * Generate content for analysis tasks
   */
  async generateAnalysisContent(
    prompt: string,
    context?: string,
    options: ThinkingTaskOptions = {}
  ): Promise<string> {
    const messages: OpenRouterMessage[] = [
      {
        role: 'system',
        content: `You are an expert analyst with deep reasoning capabilities. Provide comprehensive, well-structured analysis with clear reasoning and evidence-based conclusions.`
      }
    ];

    if (context) {
      messages.push({
        role: 'user',
        content: `Context: ${context}`
      });
    }

    messages.push({
      role: 'user',
      content: prompt
    });

    const response = await this.makeRequest(messages, {
      temperature: options.temperature || 0.3,
      max_tokens: options.maxTokens || 4000,
      top_p: options.topP || 0.9
    });

    return response.choices[0]?.message?.content || '';
  }

  /**
   * Generate content for strategic planning tasks
   */
  async generateStrategyContent(
    prompt: string,
    requirements: string[],
    options: ThinkingTaskOptions = {}
  ): Promise<string> {
    const messages: OpenRouterMessage[] = [
      {
        role: 'system',
        content: `You are a strategic planning expert with advanced reasoning capabilities. Create comprehensive, actionable strategies with detailed implementation guidance.`
      },
      {
        role: 'user',
        content: `Requirements: ${requirements.join(', ')}\n\n${prompt}`
      }
    ];

    const response = await this.makeRequest(messages, {
      temperature: options.temperature || 0.5,
      max_tokens: options.maxTokens || 5000,
      top_p: options.topP || 0.9
    });

    return response.choices[0]?.message?.content || '';
  }

  /**
   * Generate content for quality assessment tasks
   */
  async generateQualityAssessment(
    content: string,
    criteria: string[],
    options: ThinkingTaskOptions = {}
  ): Promise<string> {
    const messages: OpenRouterMessage[] = [
      {
        role: 'system',
        content: `You are a quality assessment expert with meticulous attention to detail. Provide thorough, objective evaluations with specific recommendations for improvement.`
      },
      {
        role: 'user',
        content: `Assess the following content against these criteria: ${criteria.join(', ')}\n\nContent to assess:\n${content}`
      }
    ];

    const response = await this.makeRequest(messages, {
      temperature: options.temperature || 0.2,
      max_tokens: options.maxTokens || 3000,
      top_p: options.topP || 0.8
    });

    return response.choices[0]?.message?.content || '';
  }

  private buildThinkingSystemPrompt(depth: 'shallow' | 'medium' | 'deep'): string {
    const basePrompt = `You are Qwen3-235B, an advanced AI model with exceptional reasoning capabilities. You excel at deep thinking, analysis, and problem-solving.`;

    const depthInstructions = {
      shallow: `Provide clear, concise analysis with basic reasoning steps.`,
      medium: `Think through problems systematically, showing your reasoning process and considering multiple perspectives.`,
      deep: `Engage in deep, thorough analysis. Break down complex problems into components, consider multiple angles, evaluate evidence, and provide comprehensive reasoning with detailed explanations.`
    };

    return `${basePrompt}\n\n${depthInstructions[depth]}\n\nAlways structure your responses clearly and provide actionable insights.`;
  }

  private enhancePromptForThinking(prompt: string, options: ThinkingTaskOptions): string {
    if (!options.enableThinking) {
      return prompt;
    }

    return `<thinking>
Let me think through this systematically:

1. Understanding the task and requirements
2. Analyzing the key components and relationships
3. Considering different approaches and perspectives
4. Evaluating potential solutions and outcomes
5. Synthesizing insights into actionable recommendations
</thinking>

${prompt}

Please provide a comprehensive response with clear reasoning and detailed analysis.`;
  }

  private async makeRequest(
    messages: OpenRouterMessage[],
    parameters: Record<string, any>
  ): Promise<OpenRouterResponse> {
    const requestBody = {
      model: this.config.model,
      messages,
      ...parameters
    };

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.maxRetries!; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

        const response = await fetch(`${this.config.baseURL}/chat/completions`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
            'X-Title': 'AI Content System'
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
        }

        const data: OpenRouterResponse = await response.json();
        return data;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.error(`OpenRouter request attempt ${attempt} failed:`, lastError.message);

        if (attempt < this.config.maxRetries!) {
          // Exponential backoff
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error('All OpenRouter request attempts failed');
  }

  /**
   * Test the connection to OpenRouter
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.generateThinkingContent(
        'Test connection. Respond with "Connection successful" if you can read this.',
        { maxTokens: 50, temperature: 0.1 }
      );
      return response.toLowerCase().includes('connection successful');
    } catch (error) {
      console.error('OpenRouter connection test failed:', error);
      return false;
    }
  }

  /**
   * Get model information
   */
  getModelInfo(): { model: string; capabilities: string[] } {
    return {
      model: this.config.model!,
      capabilities: [
        'Advanced reasoning and thinking',
        'Complex problem solving',
        'Strategic analysis',
        'Quality assessment',
        'Multi-perspective evaluation',
        'Detailed explanations'
      ]
    };
  }
}
