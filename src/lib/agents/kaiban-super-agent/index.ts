/**
 * KaibanJS Super Agent System
 * 
 * Main orchestration system for the KaibanJS-based super agent workflow.
 * Integrates Gemini and Qwen models with Tavily search following the
 * super agent workflow pattern.
 * 
 * Features:
 * - 7-phase super agent workflow
 * - <PERSON>wen model for high reasoning phases (1, 4, 7)
 * - Gemini model for other phases
 * - Tavily search integration
 * - Comprehensive error handling and logging
 */

import { Team } from 'kaibanjs';
import { superAgentTeam, agentConfig } from './agents';
import { superAgentTasks, taskConfig } from './tasks';
import { validateTavilyTool } from './tools';

// Console logging utility for Kaiban System
const logKaibanSystem = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`🚀 [KAIBAN-SYSTEM] ${timestamp}: ${message}`);
  if (data) {
    console.log(`📝 [KAIBAN-SYSTEM-DATA]:`, data);
  }
};

/**
 * Super Agent Options Interface
 */
export interface SuperAgentOptions {
  topic: string;
  contentType?: 'article' | 'blog-post' | 'research-paper' | 'comprehensive-guide' | 'case-study' | 'white-paper' | 'tutorial';
  targetWordCount?: number;
  tone?: 'professional' | 'casual' | 'academic' | 'conversational' | 'authoritative' | 'engaging' | 'technical';
  targetAudience?: 'beginner' | 'intermediate' | 'expert' | 'general' | 'technical' | 'business';
  maxPrimaryResults?: number;
  maxDeepResults?: number;
  enableFactChecking?: boolean;
  includeSourceCitations?: boolean;
}

/**
 * Super Agent Result Interface
 */
export interface SuperAgentResult {
  success: boolean;
  content?: string;
  title?: string;
  wordCount?: number;
  sourcesUsed?: number;
  qualityScore?: number;
  executionTime?: number;
  phaseResults?: Record<string, any>;
  error?: string;
  metadata?: {
    totalTasks: number;
    completedTasks: number;
    failedTasks: number;
    agentsUsed: string[];
    modelsUsed: string[];
    searchQueriesExecuted: number;
  };
}

/**
 * Progress Callback Interface
 */
export interface ProgressCallback {
  (phase: string, progress: number, message: string): void;
}

/**
 * KaibanJS Super Agent Class
 */
export class KaibanSuperAgent {
  private team: Team;
  private isInitialized: boolean = false;
  private onProgress?: ProgressCallback;

  constructor(onProgress?: ProgressCallback) {
    this.onProgress = onProgress;
    logKaibanSystem('Initializing KaibanJS Super Agent System');
    
    // Validate tools before initialization
    if (!validateTavilyTool()) {
      throw new Error('Tavily tool validation failed. Please check your API key configuration.');
    }

    // Create the KaibanJS team
    this.team = new Team({
      name: 'Super Agent Team',
      agents: superAgentTeam,
      tasks: superAgentTasks,
      inputs: {
        topic: '',
        contentType: 'article',
        targetAudience: 'intermediate',
        wordCount: 2000,
        tone: 'professional'
      },
      env: {
        OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY,
        GEMINI_API_KEY: process.env.GEMINI_API_KEY,
        TAVILY_API_KEY: process.env.TAVILY_API_KEY,
      }
    });

    this.isInitialized = true;
    
    logKaibanSystem('KaibanJS Super Agent System initialized successfully', {
      totalAgents: agentConfig.totalAgents,
      totalTasks: taskConfig.totalTasks,
      toolsConfigured: ['TavilySearchResults'],
      modelsConfigured: ['qwen/qwen3-235b-a22b-04-28', 'gemini-2.0-flash']
    });
  }

  /**
   * Execute the super agent workflow
   */
  async execute(options: SuperAgentOptions): Promise<SuperAgentResult> {
    if (!this.isInitialized) {
      throw new Error('KaibanJS Super Agent System not initialized');
    }

    const startTime = Date.now();
    
    logKaibanSystem('Starting super agent workflow execution', {
      topic: options.topic,
      contentType: options.contentType,
      targetWordCount: options.targetWordCount,
      targetAudience: options.targetAudience
    });

    try {
      // Update progress
      this.updateProgress('initialization', 0, 'Initializing workflow...');

      // Set team inputs
      await this.team.start({
        topic: options.topic,
        contentType: options.contentType || 'article',
        targetAudience: options.targetAudience || 'intermediate',
        wordCount: options.targetWordCount || 2000,
        tone: options.tone || 'professional'
      });

      // Execute the workflow
      this.updateProgress('execution', 10, 'Starting task execution...');
      
      const result = await this.team.waitForCompletion();
      
      const executionTime = Date.now() - startTime;
      
      logKaibanSystem('Super agent workflow completed successfully', {
        executionTime: `${executionTime}ms`,
        tasksCompleted: taskConfig.totalTasks
      });

      // Process and return results
      return this.processResults(result, executionTime);

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      logKaibanSystem('Super agent workflow failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: `${executionTime}ms`
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        executionTime,
        metadata: {
          totalTasks: taskConfig.totalTasks,
          completedTasks: 0,
          failedTasks: taskConfig.totalTasks,
          agentsUsed: agentConfig.agentNames,
          modelsUsed: ['qwen/qwen3-235b-a22b-04-28', 'gemini-2.0-flash'],
          searchQueriesExecuted: 0
        }
      };
    }
  }

  /**
   * Process workflow results
   */
  private processResults(result: any, executionTime: number): SuperAgentResult {
    try {
      // Extract content from the final task (Quality Assurance)
      const finalOutput = result.tasks?.[taskConfig.totalTasks - 1]?.output || result.output;
      
      return {
        success: true,
        content: finalOutput,
        title: this.extractTitle(finalOutput),
        wordCount: this.countWords(finalOutput),
        sourcesUsed: this.countSources(result),
        qualityScore: this.calculateQualityScore(result),
        executionTime,
        phaseResults: this.extractPhaseResults(result),
        metadata: {
          totalTasks: taskConfig.totalTasks,
          completedTasks: taskConfig.totalTasks,
          failedTasks: 0,
          agentsUsed: agentConfig.agentNames,
          modelsUsed: ['qwen/qwen3-235b-a22b-04-28', 'gemini-2.0-flash'],
          searchQueriesExecuted: this.countSearchQueries(result)
        }
      };
    } catch (error) {
      logKaibanSystem('Error processing results', { error });
      
      return {
        success: false,
        error: 'Failed to process workflow results',
        executionTime,
        metadata: {
          totalTasks: taskConfig.totalTasks,
          completedTasks: 0,
          failedTasks: 1,
          agentsUsed: agentConfig.agentNames,
          modelsUsed: ['qwen/qwen3-235b-a22b-04-28', 'gemini-2.0-flash'],
          searchQueriesExecuted: 0
        }
      };
    }
  }

  /**
   * Update progress callback
   */
  private updateProgress(phase: string, progress: number, message: string): void {
    if (this.onProgress) {
      this.onProgress(phase, progress, message);
    }
  }

  /**
   * Helper methods for result processing
   */
  private extractTitle(content: string): string {
    const lines = content.split('\n');
    const titleLine = lines.find(line => line.trim().startsWith('#') || line.trim().length > 10);
    return titleLine?.replace(/^#+\s*/, '').trim() || 'Generated Content';
  }

  private countWords(content: string): number {
    return content.split(/\s+/).filter(word => word.length > 0).length;
  }

  private countSources(result: any): number {
    // Count unique sources from research tasks
    const sources = new Set();
    result.tasks?.forEach((task: any) => {
      if (task.output && typeof task.output === 'string') {
        const urls = task.output.match(/https?:\/\/[^\s]+/g) || [];
        urls.forEach((url: string) => sources.add(url));
      }
    });
    return sources.size;
  }

  private calculateQualityScore(result: any): number {
    // Simple quality scoring based on completion and content length
    const completedTasks = result.tasks?.filter((task: any) => task.status === 'completed').length || 0;
    const completionScore = (completedTasks / taskConfig.totalTasks) * 100;
    return Math.round(completionScore);
  }

  private extractPhaseResults(result: any): Record<string, any> {
    const phaseResults: Record<string, any> = {};
    
    result.tasks?.forEach((task: any, index: number) => {
      const phaseName = taskConfig.taskNames[index];
      phaseResults[phaseName] = {
        status: task.status || 'completed',
        output: task.output,
        agent: agentConfig.agentNames[index],
        executionTime: task.executionTime || 0
      };
    });
    
    return phaseResults;
  }

  private countSearchQueries(result: any): number {
    // Count search queries from research tasks
    let queryCount = 0;
    result.tasks?.forEach((task: any) => {
      if (task.output && typeof task.output === 'string') {
        // Look for search-related content
        const searchMatches = task.output.match(/search|query|tavily/gi) || [];
        queryCount += searchMatches.length;
      }
    });
    return queryCount;
  }
}

/**
 * Export configuration and utilities
 */
export { agentConfig, taskConfig };
export { superAgentTeam } from './agents';
export { superAgentTasks } from './tasks';
export { kaibanTools } from './tools';

logKaibanSystem('KaibanJS Super Agent System module loaded successfully');
